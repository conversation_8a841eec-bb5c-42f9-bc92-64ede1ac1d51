<template>
  <view class="">
    <!-- 顶部导航 -->
    <view v-if="$vhFrom == '' || $vhFrom == 'next'" class="">
      <vh-navbar title="余额充值" />
    </view>
    <view v-if="!loading" class="fade-in">
      <view class="balance-card-strict">
			<view class="balance-card-content">
				<view class="flex-col-sb-s w-p100"> 
					<view class="font-26 text-9">账号余额（元）</view>
					<view class="font-56 font-wei text-3 mt-16">{{balance.toFixed(2) || '0.00'}}</view>
				</view>

			</view>
		</view>
    
    <!-- 充值优惠选项 -->
    <view class="bg-ffffff b-rad-12 b-sh-00021200-022 ptb-30-plr-32 ml-24 mr-24 mt-20">
      <view class="text-center font-32 text-3 font-wei-500 mb-30">充值享优惠</view>
      <view class="d-flex flex-wrap j-sb mb-30">
        <view
          v-for="(item, index) in rechargeOptions"
          :key="index"
           :class="{ 'b-s-01-e80404-r': selectedOption === index, 'b-s-01-eeeeee': selectedOption !== index }"
          class="w-p48 h-180 d-flex j-center a-center bg-ffffff b-rad-08 mb-24"
          @click="selectOption(index)"
        >
          <view class="text-center">
            <view class="font-28 text-3 mb-08">充￥ 
              <text class="font-34">{{item.price}}</text>
              </view>
            <view class="font-24 text-e80404">赠￥{{item.gift_amount}}</view>
          </view>
        </view>
      </view>
      
      <!-- 兑换卡充值入口   <view class=" pt-20  font-28 text-3 wp-100" @click="goToExchangeCard">
        <view class="text-center">兑换卡充值  ></view>
        
      </view>  -->
    
    </view>
    
    <!-- 底部协议与按钮 -->
    <view class="p-fixed bottom-0 w-p100 bg-ffffff b-sh-00001002-007 p-b-safe-area">
      <view class="d-flex a-center ptb-16-plr-24 bt-s-01-f8f8f8">
       
        <vh-check :checked="isAgree" @click="toggleAgree" />
        <view class="font-24 text-9 ml-10">
          资金由商家收取，我已阅读并同意
          <text class="text-e80404" @click.stop="viewAgreement">《储值协议》</text>
        </view>
      </view>
      
      <view class="pl-24 pr-24 pb-62">
        <u-button 
         :ripple="true" ripple-bg-color="#ffffff"
          :custom-style="{ width:'646rpx',color:'#fff',backgroundColor: !canRecharge ? '#FCE4E3' : '#E80404', border:'none'}"

          :disabled="!canRecharge"
          shape="circle"
          @click="recharge"
        >立即充值</u-button>
      </view>
    </view>
    </view>
    <!-- 骨架屏 -->
		<vh-skeleton v-else :type="10000" loading-mode="flower" />
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  data() {
    return {
      balance: '0',
      rechargeOptions: [],
      selectedOption: -1,
      isAgree: false,
      loading:true
    }
  },
  
  onLoad() {
    this.getBalanceInfo();
    this.requestGiftCardList();
    uni.setNavigationBarTitle({
        title: '余额充值',
      })
  },
  // 获取余额信息
 
  computed: {
    ...mapState(['routeTable']),
    canRecharge() {
      return this.selectedOption !== -1 && this.isAgree;
    }
  },
  methods: {
    async getBalanceInfo() {
        let res = await this.$u.api.myCurrentBalance()
					const data = res.data;
					this.balance = data.recharge_balance + data.bonus_balance;
		},
   
    selectOption(index) {
      this.selectedOption = index;
    },
    toggleAgree() {
      this.isAgree = !this.isAgree;
    },
    viewAgreement() {
      // 查看协议
      // uni.navigateTo({
      //   url: '/pages/webview/webview?url=协议地址&title=使用协议'
      // });
    },

    async requestGiftCardList(){
      this.loading = true;
      let res = await this.$u.api.giftCardList({page:1, limit:20, type:1})
      const {total, list} = res.data;
      this.loading = false;
      this.rechargeOptions = list;
    },
    goToExchangeCard() {
      // 跳转到兑换卡充值页面
     
      this.jump.appAndMiniJump(0, this.routeTable.pJCardExchange, this.$vhFrom)
    },
    async recharge() {
      if (!this.canRecharge) return;
      
      const option = this.rechargeOptions[this.selectedOption];
      
      this.feedback.loading({ title: '正在下单...' })
      try {
        const data = {
            goods_id:option.id,
            order_from: this.$client,
            type:1,
            order_qty:1
        };
        let res = await this.$u.api.giftCardOrderCreate(data)
        let payInfo = {
          payPlate: 60,
          ...res.data, //下单信息
        }
        // uni.removeStorageSync('source') //下单成功后需要清除来源标识
        // this.muPayInfo(payInfo)
        if(this.$app){
            const appData = {
						paylmfor: payInfo,
						type: 60,
						priceString: payInfo.payment_amount,
						androidMainOrderNo: payInfo.order_no,
						androidFrom: '60'
					}
					this.jump.jumpAppPayment(this.$vhFrom, appData)
          if(this.$vhFrom != 'next'){
            wineYunJsBridge.openAppPage({
            client_path: { "ios_path":"finish", "android_path":"finish" }
           })
          }
        } else {
          this.muPayInfo(payInfo)
          this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
        }
        // this.jump.redirectTo(this.routeTable.pBPayment)
       
      } catch (e) {
        console.log(e)
        // setTimeout(() => {
        // 	this.jump.navigateBack()
        // }, 1500)
      }
    
    }
  }
}
</script>

<style lang="scss" scoped>
.balance-card-strict {
  margin: 32rpx 24rpx 0 24rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.08);
}
.balance-card-content {
  padding: 36rpx 32rpx 24rpx 32rpx;
  display: flex;
}
</style>
