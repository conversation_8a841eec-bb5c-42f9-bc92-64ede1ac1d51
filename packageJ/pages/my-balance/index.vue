<template>
	<view class="content">
		<!-- 导航栏 -->
		<!-- <view v-if="$vhFrom ==''" class="">
			<vh-navbar title="我的余额" />
		</view> -->
		<view v-if="$vhFrom == '' || $vhFrom == 'next'" class="bb-s-01-eeeeee">
			<vh-navbar back-icon-color="#333" title="我的余额" title-size="36" :title-bold="true" title-color="#333" />
		</view>
		<!-- 余额卡片 -->
		 <view >
			<view class="balance-card-strict">
			<view class="balance-card-content">
				<view class="flex-col-sb-s w-p100"> 
					<view class="font-26 text-3">账号余额（元）</view>
					<view class="font-56 font-wei text-3 mt-16">{{balanceInfo.balance.toFixed(2) || '0.00'}}</view>
				</view>
				
				<view class="balance-btn-wrap">
					<u-button class="vh-btn" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'144rpx', height:'52rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
					@click="onRecharge">充值</u-button>
				</view>
			</view>
		</view>

		<!-- 选项卡 -->
		
		<view class="mt-20 bb-s-01-eeeeee">
			<u-tabs :is-scroll="false" :list="tabsList" :current="currentType" bar-width="36" bar-height="8" font-size="28" inactive-color="#999" active-color="#333"
			:bar-style="{background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)'}" @change="switchTab" />
		</view>

		<!-- 交易记录列表 -->
		<view  v-if="getActiveList.length " class="transaction-list-strict">
			<view >
				<view v-for="(item, index) in rechargeList" :key="index" class="transaction-row-strict">
					<view class="row-main">
						<view class="d-flex a-center">
							<image :src="ossIcon(item.imageName)" class="w-24 h-24" />
							<view class="font-26 text-3 font-wei-500 l-h-30 ml-10">{{item.name}}</view>
						 </view>
						
						<view class="ml-34 font-22 text-9 l-h-30 mt-10">{{item.title}}</view>
						<view class="ml-34 font-22 text-9 mt-08 l-h-30 mt-04">{{item.time}}</view>
					</view>
					<view class="d-flex a-center">
						<text class="row-amount plus">{{item.type === 1 ? '+' : '-'}} {{item.amount}}</text>
					</view>
					
				</view>
			</view>
			
		</view>
		<vh-empty 
					v-else 
					:padding-top="80" 
					:padding-bottom="350" 
					:image-src="ossIcon(`/empty/emp_goods.png`)" 
					text="暂无记录" 
					
				/>
		 </view>
		
		
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'my-balance',
		
		data(){
			return{
				loading: true, //加载状态 true = 加载中、false = 结束加载
				balanceInfo: {
					balance: '0'
				},
				
				currentType: 0,
				tabsList: [
					{ name: '充值' }, 
					{ name: '消费' },
					{ name: '其他' }
				],
				rechargeList: [],
				recordImageName:'',
				showRechargePopup: false,
				selectedRechargeOption: 100,
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
				from: '', //从哪个端进入 1 = 安卓、2 = ios"
				rechargeOptions: [
					{
						label: '¥50',
						value: 50
					},
					{
						label: '¥100',
						value: 100
					},
					{
						label: '¥200',
						value: 200
					},
					{
						label: '¥500',
						value: 500
					},
					{
						label: '¥1000',
						value: 1000
					},
					{
						label: '其他金额',
						value: 0
					}
				]
			}
		},
		
		computed: {
			...mapState(['routeTable']),
			getActiveList() {
				return this.rechargeList
			},
			
		},
		onShow(){
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		onLoad(options) {
			uni.setNavigationBarTitle({
        title: '我的余额',
      })
			
		},
		
		methods: {
			async init(){
				this.getBalanceInfo()
				this.getTransactionList()
			},
			// 获取余额信息
			async getBalanceInfo() {
				try {
					// 模拟API调用
					// let res = await this.$u.api.getBalanceInfo()
					// this.balanceInfo = res.data
					let res = await this.$u.api.myCurrentBalance()
					const data = res.data;
					this.balanceInfo.balance = data.recharge_balance + data.bonus_balance;
					console.log('3444------', data);
					
				} catch(e) {
					this.feedback.toast({ title: '获取余额信息失败!'})
				}
			},
			
			
			// 获取交易记录
			async getTransactionList() {
				this.loading =true;
					const type = this.currentType+1;
					let res = await this.$u.api.myBalanceHistory({type, page: this.page, limit: this.limit })
					const { list, total } = res.data //解构商品聚合信息
					
					// 格式化数据
					const formattedList = list.map(item => {
						const isRecharge = item.type === 1;
						let name = '';
						let firsttitle = '';
						let imageName = '';
						// 根据operation_type设置name
						switch(item.operation_type) {
							case 1: firsttitle = '余额充值'; break;
							case 2: firsttitle = '虚拟卡充值'; break;
							case 3: firsttitle = '实体卡充值'; break;
							case 4: firsttitle = '官方赔偿'; break;
							case 5: firsttitle = '商品购买'; break;
							case 6: firsttitle = '订单转余额'; break;
							case 7: firsttitle = '退卡'; break;
							default: firsttitle = '其他操作';
						}
						switch(item.operation_type) {
							case 1: 
							case 2: 
							case 3: name = '充值'; imageName = '/mine/balance_recharge.png';  break;
							
							case 5:  name = '消费';imageName = '/mine/balance_consumption.png'; break;
							default: name = '其他'; imageName = '/mine/balance_other.png';;
						}
						
						return {
							name,
							title: item.operation_type===5 ? `${firsttitle}${ (+item.recharge_amount + +item.bonus_amount).toFixed(2) + '元'}` : `${firsttitle}${ item.recharge_amount + '元'}${item.bonus_amount && item.bonus_amount != '0.00' ? '，赠金' + item.bonus_amount + '元' : ''}`,
							amount: ( +item.recharge_amount + +item.bonus_amount).toFixed(2),
							time: item.change_time,
							type: item.type,
							imageName
						}
					});
					
					// 根据当前选项卡处理对应的列表数据
					const listName = 'rechargeList'
					
					this.handlePage(listName, formattedList, total);
					this.loading = false;
			},
			
			// 切换选项卡
			switchTab(tab) {
				this.currentType = tab
				this.page = 1
				this.loadStatus = 'loadmore'
				this.getTransactionList()
			},
			
			// 点击充值按钮
			onRecharge() {
				this.jump.appAndMiniJump(2, this.routeTable.pJBalanceRecharge, this.$vhFrom)
			},
			// 处理分页信息 listName = 列表名称（goodsList = 兔头商品、couponList = 优惠券）、resList = 接口返回的列表数据、total = 总条数
			handlePage(listName, resList , total) {
				this.page == 1 ? this[listName] = resList : this[listName] = [...this[listName], ...resList]
				this.totalPage = Math.ceil(total / this.limit)
				console.log('12344555555555-------', this.totalPage);
				
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.feedback.hideLoading()
			},
			onPullDownRefresh() {
			this.page = 1
			this.init()
			this.getTransactionList()
		},
		
		onPageScroll(res) {
			res.scrollTop <= 100 ? this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})` : 
			this.navBackgroundColor = `rgba(224, 20, 31, 1)`
		},
		
			onReachBottom() {
			if (this.page == this.totalPage) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getTransactionList()
		}
		}
	}
</script>

<style>
@import '@/common/css/comm.css';

.balance-card-strict {
  margin: 32rpx 24rpx 0 24rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.08);
}
.balance-card-content {
  padding: 36rpx 32rpx 24rpx 32rpx;
  display: flex;
}
.balance-btn-wrap {
  display: flex;
  justify-content: flex-end;
  margin-top: 24rpx;
}

.tab-strict-wrap {
  margin: 32rpx 24rpx 0 24rpx;
  display: flex;
  background: #fff;
  border-radius: 12rpx 12rpx 0 0;
  border-bottom: 1px solid #eee;
}
.tab-strict {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #888;
  padding: 24rpx 0 18rpx 0;
  font-weight: 500;
  position: relative;
}
.tab-strict.active {
  color: #E80404;
  font-weight: bold;
}
.tab-strict.active::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 48rpx;
  height: 6rpx;
  background: #E80404;
  border-radius: 3rpx;
}

.transaction-list-strict {
  margin: 0 24rpx;
  background: #fff;
  border-radius: 0 0 20rpx 20rpx;
  min-height: 300rpx;
}
.transaction-row-strict {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0 10rpx 0;
  border-bottom: 1px solid #eee;
}
.transaction-row-strict:last-child {
  border-bottom: none;
}
.row-main {
  flex: 1;
  min-width: 0;
}
.row-amount {
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 24rpx;
  min-width: 100rpx;
  text-align: right;
  
}
.row-amount.plus {
  color: #E80404;
}
.row-amount.minus {
  color: #222;
}

.recharge-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.recharge-option-item {
  width: 180rpx;
  height: 80rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
}
.active-option {
  border-color: #E80404;
  color: #E80404;
}
</style>
