<template>
  <view class="gift-card-container bg-ffffff">
    <!-- 顶部标题 -->
    <view v-if="$vhFrom == '' || $vhFrom == 'next'" class="">
      <vh-navbar title="礼品卡" />
    </view>
    
    <!-- 导航选项卡 -->
    <u-tabs :is-scroll="false" :list="mainTabs" :current="mainTabIndex" bar-width="36" bar-height="8" font-size="32" inactive-color="#999" active-color="#333"
			:bar-style="{background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)'}" @change="onMainTabChange" />
    <!-- 购买礼品卡内容 -->
    <view v-if="activeTab === 'buy'" class="tab-content">
      <!-- 购买步骤 -->
      <view class="buy-steps flex-sb-c">
        <view class="step-item flex-c-c">
          <view class="step-circle flex-c-c text-ffffff" :class="currentStep >= 1 ? 'bg-e80404' : 'bg-d8d8d8'">1</view>
          <text class="step-text font-26" :class="currentStep >= 1 ? 'text-e80404' : 'text-9'">选择礼品卡</text>
        </view>
        <view class="step-line" :class="currentStep >= 2 ? 'bg-e80404' : 'bg-d8d8d8'"></view>
        <view class="step-item flex-c-c">
          <view class="step-circle flex-c-c text-ffffff" :class="currentStep >= 2 ? 'bg-e80404' : 'bg-d8d8d8'">2</view>
          <text class="step-text font-26" :class="currentStep >= 2 ? 'text-e80404' : 'text-9'">支付购买</text>
        </view>
        <view class="step-line" :class="currentStep >= 3 ? 'bg-e80404' : 'bg-d8d8d8'"></view>
        <view class="step-item flex-c-c">
          <view class="step-circle flex-c-c text-ffffff" :class="currentStep >= 3 ? 'bg-e80404' : 'bg-d8d8d8'">3</view>
          <text class="step-text font-26" :class="currentStep >= 3 ? 'text-e80404' : 'text-9'">赠送好友</text>
        </view>
      </view>
      
      <!-- 礼品卡列表 -->
      <view class="gift-card-list">
        <view v-if="rechargeOptions.length > 0">
          <view class="card-row flex-sb-c" v-for="(rowCards, rowIndex) in chunkedCards" :key="rowIndex">
            <view class="card-item" v-for="card in rowCards" :key="card.id" @click="selectCard(card)">
              <view class="card-inner " :style="{ backgroundImage: `url(${card.background})`, backgroundSize: 'cover', }">
                <!-- <img
                style="width: 100%; height: 100%; object-fit: cover;"
                  :src="card.background"
                  alt=""
                /> -->
                <!-- <view class="card-value font-32 text-ffffff font-wei">{{card.title}}</view> -->
              </view>
            </view>
          </view>
        </view>
       
      </view>
      
      <!-- 查看流程 -->
      <!-- <view class="check-process ">
        <text class="font-26 text-9 ml-12">查看流程 ></text>
      </view> -->
    </view>
    
    <!-- 我的礼品卡内容 -->
    <view v-if="activeTab === 'my'" class="tab-content">
      <!-- 子选项卡 -->
      
      <u-tabs   v-if="activeTab === 'my'" :is-scroll="false" :list="subTabs" :current="subTabIndex" bar-width="36" bar-height="8" font-size="28" inactive-color="#999" active-color="#333"
			:bar-style="{background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)'}" @change="onSubTabChange" />
    <!-- 购买礼品卡内容 -->
      <!-- 我的礼品卡列表 -->
      <view class="my-gift-card-list">
        <!-- 可赠送礼品卡列表 -->
        <view v-if="activeSubTab === 'available'">
          <view v-if="myGiftCards.available.length > 0">
            <view class="my-card-item" v-for="card in myGiftCards.available" :key="card.card_no">
              <view class="card-wrapper">
                <view class="my-card-inner" :style="{ backgroundImage: `url(${card.background})`,backgroundRepeat: 'no-repeat'  }">
                  <!-- <text class="my-card-value font-32 text-e80404 font-wei">{{card.amount}}元充值卡</text> -->
                </view>
                <view class="d-flex j-sb a-center mt-20"> 
                  <view> 
                    <view class="font-24">购买日期</view>
                    <view class="font-24">{{ card.create_time }}</view>
                  </view>
                  <view class="action-wrapper">
                  <view class="action-btn bg-fe6c4f flex-c-c" @click="useCard(card)">
                    <text class="text-ffffff font-28">自己充</text>
                  </view>
                  <view class="action-btn bg-fe6c4f flex-c-c ml-20" @click="giftCard(card)">
                    <text class="text-ffffff font-28">赠送他人</text>
                  </view>
                </view>
                </view>
              
              </view>
            </view>
          </view>
          <vh-empty v-else :padding-top="52" :padding-bottom="400" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png" text="暂无可赠送礼品卡~" :text-bottom="0" />
          <!-- <view v-else class="empty-tip flex-c-c">
            <text class="text-9 font-28">暂无可赠送礼品卡</text>
          </view> -->
        </view>
        
        <!-- 已送出礼品卡列表 -->
        <view v-if="activeSubTab === 'sent'">
          <view v-if="myGiftCards.sent.length > 0">
            <view class="my-card-item" v-for="card in myGiftCards.sent" :key="card.card_no">
              <view class="card-wrapper">
                <view class="my-card-inner " :style="{ backgroundImage: `url(${card.background})`,backgroundRepeat: 'no-repeat'  }">
                  <!-- <text class="my-card-value font-32 text-e80404 font-wei">{{card.amount}}元充值卡</text> -->
                </view>
                <view class="d-flex j-sb a-center mt-20">   
                  <view> 
                    <view class="font-24">转赠日期</view>
                    <view class="font-24">{{ card.transfer_time }}</view>
                  </view>
                  <view class="action-wrapper">
                  <view class="action-btn bg-999999 flex-c-c">
                    <text class="text-ffffff font-28">已赠送</text>
                  </view>
                </view>
                </view>
               
              </view>
            </view>
          </view>
          <vh-empty v-else :padding-top="52" :padding-bottom="400" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png" text="暂无已赠送礼品卡~" :text-bottom="0" />
        </view>
        
        <!-- 已使用礼品卡列表 -->
        <view v-if="activeSubTab === 'used'">
          <view v-if="myGiftCards.used.length > 0">
            <view class="my-card-item" v-for="card in myGiftCards.used" :key="card.card_no">
              <view class="card-wrapper">
                <view class="my-card-inner" :style="{ backgroundImage: `url(${card.background})`,backgroundRepeat: 'no-repeat'  }">
                  <!-- <text class="my-card-value font-32 text-e80404 font-wei">{{card.amount}}元充值卡</text> -->
                </view>
                <view class="d-flex j-sb a-center mt-20">
                  <view> 
                    <view class="font-24">使用日期</view>
                    <view class="font-24">{{ card.use_time }}</view>
                  </view>
                  <view class="action-wrapper">
                  <view class="action-btn bg-999999 flex-c-c" >
                    <text class="text-ffffff font-28">已使用</text>
                  </view>
                </view>
                </view>  
               
              </view>
            </view>
          </view>
          <vh-empty v-else :padding-top="52" :padding-bottom="400" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png" text="暂无已使用礼品卡~" :text-bottom="0" />
        </view>
      </view>
      
      <!-- 加载更多 -->
      <u-loadmore v-if="loadStatus !== 'nomore'" :status="loadStatus" />
    </view>
    
    <!-- 自己充值弹出层 -->
    <u-popup v-model="showUseCardPopup" mode="bottom" width="100%" :mask-close-able="true" border-radius="20">
      <view class="use-card-popup">
        <view class="gift-card-info">
          <text class="gift-card-title font-26 text-9">礼品卡充值</text>
         
          <view class="use-description font-28 text-3 mt-20 ml-10">
            <text>使用一张{{selectedUseCard.amount}}元充值卡进行充值。</text>
          </view>
          <view class="bg-ffffff p-rela d-flex  a-center pt-20 pb-32">
			<vh-check :checked="isAgreeReg" @click="isAgreeReg = !isAgreeReg" />
			<text class="ml-10 font-24" :class="isAgreeReg ? 'text-3' : 'text-9'">我已阅读并同意</text>
			<text class="font-24 text-3" @click="jump.jumpH5Agreement(agreementPrefix + '/personApprove')">《酒云网个人认证用户管理条例》</text>
		</view>
        <view class="bg-ffffff d-flex j-center pb-32">
			<u-button :disabled="!isAgreeReg" shape="circle" :ripple="true" ripple-bg-color="#ffffff" 
			:custom-style="{ width:'646rpx',color:'#fff',backgroundColor: !isAgreeReg ? '#FCE4E3' : '#E80404', border:'none'}" @click="confirmUseCard">立即充值</u-button>
		</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  data() {
    return {
      activeTab: 'buy', // 当前活动的主标签页：buy-购买礼品卡，my-我的礼品卡
      activeSubTab: 'available', // 当前活动的子标签页：available-可赠送，sent-已送出，used-已使用
      currentStep: 0, // 当前购买步骤：1-选择礼品卡，2-支付购买，3-赠送好友
      selectedCard: null, // 选中的卡面值
      loading: false, // 是否正在加载
      loadStatus: 'loadmore', // 加载状态：loadmore-加载更多，loading-加载中，nomore-没有更多了
      page: 1, // 当前页码
      limit: 10, // 每页数量
      totalPage: 1, // 总页数
      rechargeOptions: [], // 充值卡选项
      myGiftCards: { // 我的礼品卡
        available: [], // 可赠送
        sent: [], // 已送出
        used: [] // 已使用
      },
      showUseCardPopup: false, // 是否显示使用礼品卡弹窗
      selectedUseCard: {}, // 当前选择要使用的礼品卡
      isAgreeReg: false,//是否勾选协议
      mainTabs: [
        { name: '购买礼品卡', key: 'buy' },
        { name: '我的礼品卡', key: 'my' }
      ],
      mainTabIndex: 0, // u-tabs 当前索引
      subTabs: [
        { name: '可赠送', key: 'available' },
        { name: '已送出', key: 'sent' },
        { name: '已使用', key: 'used' }
      ],
      subTabIndex: 0, // u-tabs 当前索引
    }
  },
  computed: {
    ...mapState(['routeTable']),
    // 将充值卡选项按每行2个进行分组
    chunkedCards() {
      let result = [];
      for (let i = 0; i < this.rechargeOptions.length; i += 2) {
        result.push(this.rechargeOptions.slice(i, i + 2));
      }
      return result;
    }
  },
  onShow() {
			this.login.isLoginV2(this.$vhFrom).then(() => {
				this.init()
			})
    
		},
  onLoad(options) {

    if(options){
       
        if(options.tab){
            this.activeTab = options.tab;
           
        }
        uni.setNavigationBarTitle({
        title: '礼品卡',
      })
      window.vhAppSendMessage = (obj = {}) => {
      console.log('礼品卡obj', obj)
      if (typeof obj === 'string') obj = JSON.parse(obj)
      const { type = 1 } = obj
      if (type === 1) return { type, data: '酒云网' }
      if (type === 4) {
        const images = JSON.parse(obj.data).images
        console.log('uploadPictureInfo', images)
        uploadPictureInfo && uploadPictureInfo(images)
        window.uploadPictureInfo = null
      }
      if (type === 6) {
        // APP收银台支付成功
        console.log('卧室礼品卡支付成功');
        
        this.activeTab = 'my';
        this.mainTabIndex = 1;
        this.subTabIndex= 0;
        this.page = 1;
        this.requestMyGiftCards();
      }
      if (type === 7) {
        // APP收银台取消支付
      }
      if (type === 8) {
        // 拉起APP支付失败
        onPullAppPayFail && onPullAppPayFail()
      }
      if (type == 10) {
        indexTopRefresh && indexTopRefresh()
      }
      if (type == 11) {
        this.handlePhotoMessage(obj)
      }
      return ''
    }
    }
    
  },
  methods: {
    async init(){
        if(this.tab == 'my'){
          console.log('showMyGift');
          
            this.page = 1;
            this.requestMyGiftCards();
        } else {
            this.requestGiftCardList();
        }
       
    },
    // 切换主标签页
    switchTab(tab) {
      this.activeTab = tab;
      this.mainTabIndex = this.mainTabs.findIndex(item => item.key === tab);
      if (tab === 'my') {
        this.page = 1;
        this.requestMyGiftCards();
      } else {
        this.requestGiftCardList();
      }
    },
    // 获取购买礼品卡列表
    async requestGiftCardList() {
      this.loading = true;
      try {
        let res = await this.$u.api.giftCardList({page: 1, limit: 20, type: 2});
        const {total, list} = res.data;
        this.rechargeOptions = list || [];
      } catch (e) {
        console.error('获取礼品卡列表失败', e);
        this.rechargeOptions = [];
      } finally {
        this.loading = false;
      }
    },
    // 获取我的礼品卡列表
    async requestMyGiftCards() {
      this.loading = true;
      this.loadStatus = 'loading';
      try {
        // 获取当前选择的类型
        let type = 1; // 默认可赠送
        if (this.activeSubTab === 'sent') type = 2;
        if (this.activeSubTab === 'used') type = 3;
        
        let res = await this.$u.api.myGiftCardsList({page: this.page, limit: this.limit, type});
        const {total, list} = res.data;
        
        // 根据类型更新对应的列表
        if (this.page === 1) {
          // 首次加载，直接赋值
          if (type === 1) this.myGiftCards.available = list || [];
          else if (type === 2) this.myGiftCards.sent = list || [];
          else if (type === 3) this.myGiftCards.used = list || [];
        } else {
          // 加载更多，合并数据
          if (type === 1) this.myGiftCards.available = [...this.myGiftCards.available, ...(list || [])];
          else if (type === 2) this.myGiftCards.sent = [...this.myGiftCards.sent, ...(list || [])];
          else if (type === 3) this.myGiftCards.used = [...this.myGiftCards.used, ...(list || [])];
        }
        
        // 计算总页数并更新加载状态
        this.totalPage = Math.ceil(total / this.limit);
        this.loadStatus = this.page >= this.totalPage ? 'nomore' : 'loadmore';
      } catch (e) {
        console.error('获取我的礼品卡列表失败', e);
        if (this.activeSubTab === 'available') this.myGiftCards.available = [];
        else if (this.activeSubTab === 'sent') this.myGiftCards.sent = [];
        else if (this.activeSubTab === 'used') this.myGiftCards.used = [];
        this.loadStatus = 'loadmore';
      } finally {
        this.loading = false;
      }
    },
    // 切换子标签页
    switchSubTab(tab) {
      this.activeSubTab = tab;
      this.subTabIndex = this.subTabs.findIndex(item => item.key === tab);
      this.page = 1;
      this.requestMyGiftCards();
    },
    // 选择礼品卡
    selectCard(card) {
      this.selectedCard = card;
      this.jump.appAndMiniJump(2,`/packageJ/pages/gift-card/select-gift-card?amount=${card.price}&id=${card.id}`, this.$vhFrom);
    },
    // 自己使用礼品卡
    useCard(card) {
      // 设置当前选中的礼品卡并显示弹窗
      this.selectedUseCard = card;
      this.showUseCardPopup = true;
    },
    // 确认使用礼品卡
    async confirmUseCard() {
      // giftCarduseVirtual
      this.loading = true;
      try {
        let res = await this.$u.api.giftCarduseVirtual({card_no: this.selectedUseCard.card_no});
        this.feedback.toast({ title: '充值成功' });
        this.page = 1;
      this.requestMyGiftCards();
       
      } catch (e) {
        console.error('获取礼品卡列表失败', e);
        
      } finally {
        this.loading = false;
      }
     
      this.showUseCardPopup = false;
    },
    // 赠送礼品卡给他人
    giftCard(card) {
      // 跳转到赠送礼品卡页面
      this.jump.appAndMiniJump(2, `/packageJ/pages/gift-card/send-gift-card?card_no=${card.card_no}&amount=${card.amount}`, this.$vhFrom);
    },
    onMainTabChange(index) {
      this.mainTabIndex = index;
      this.activeTab = this.mainTabs[index].key;
      if (this.activeTab === 'my') {
        this.page = 1;
        this.requestMyGiftCards();
      } else {
        this.requestGiftCardList();
      }
    },
    onSubTabChange(index) {
      this.subTabIndex = index;
      this.activeSubTab = this.subTabs[index].key;
      this.page = 1;
      this.requestMyGiftCards();
    },
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.page = 1;
    if (this.activeTab === 'buy') {
      this.requestGiftCardList();
    } else {
      this.requestMyGiftCards();
    }
    uni.stopPullDownRefresh();
  },
  // 加载更多
  onReachBottom() {
    if (this.activeTab === 'my' && this.page < this.totalPage) {
        console.log('3333333355555555555');
        
      this.page++;
      this.loadStatus = 'loading';
      this.requestMyGiftCards();
    }
  }
}
</script>

<style scoped>
.gift-card-container {
  min-height: 100vh;
}

.gift-card-header {
  padding: 30rpx 0;
  text-align: center;
}

/* 主标签页样式 */
.tab-container {
  width: 100%;
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
}

.tab-item {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.tab-text {
  font-size: 30rpx;
  padding-bottom: 10rpx;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 4rpx;
  border-radius: 2rpx;
}

/* 购买礼品卡内容样式 */
.tab-content {
  padding: 20rpx 30rpx;
}

.buy-steps {
  padding: 40rpx 50rpx;
}

.step-item {
  flex-direction: column;
}

.step-circle {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  margin-bottom: 10rpx;
}

.step-line {
  flex: 1;
  height: 2rpx;
  margin: 0 15rpx;
  margin-bottom: 30rpx;
}

.step-text {
  margin-top: 10rpx;
}

/* 礼品卡列表样式 */
.gift-card-list {
  padding: 20rpx 0;
}

.card-row {
  margin-bottom: 30rpx;
}

.card-item {
  width: 48%;
  aspect-ratio: 1.6;
  border-radius: 12rpx;
  overflow: hidden;
}

.card-inner {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
}

.check-process {
  justify-content: flex-end;
  margin-top: 20rpx;
}

/* 我的礼品卡标签页样式 */
.sub-tab-container {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.sub-tab-item {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 0;
}

.sub-tab-text {
  font-size: 28rpx;
  padding-bottom: 8rpx;
}

.sub-tab-line {
  position: absolute;
  bottom: 0;
  width: 50rpx;
  height: 4rpx;
  border-radius: 2rpx;
}

/* 我的礼品卡列表样式 */
.my-gift-card-list {
  padding: 30rpx 20rpx;
}

.my-card-item {
  width: 100%;
  margin-bottom: 40rpx;
  position: relative;
}

.card-wrapper {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  padding: 20rpx;
}

.my-card-inner {
  width: 100%;
  height: 280rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
  background-position: center;
  background-size: contain;
}

.bg-gradient {
  background: linear-gradient(90deg, #58cdc2, #c2eae2, #f9e8df, #f7c1c7);
}

.my-card-value {
  position: absolute;
  bottom: 20rpx;
  right: 30rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.action-wrapper {
  display: flex;
  justify-content: flex-end;
  /* margin-top: 20rpx; */
  /* padding: 10rpx 0; */
}

.action-btn {
  width: 160rpx;
  height: 70rpx;
  border-radius: 35rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.ml-20 {
  margin-left: 20rpx;
}

.bg-fe6c4f {
  background-color: #fe6c4f;
}

.empty-tip {
  height: 300rpx;
}

/* 自己充值弹出层样式 */
.use-card-popup {
  width: 100%;
  padding: 40rpx 30rpx;
  background-color: #ffffff;
}

.gift-card-info {
  padding: 20rpx;
}

.gift-card-title {
  margin-bottom: 20rpx;
}

.gift-card-display {
  width: 100%;
  height: 240rpx;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 20rpx;
  border-radius: 12rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.agreement-row {
  display: flex;
  align-items: center;
}

.use-button {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
}

.bg-e80404 {
  background-color: #e80404;
}
</style>
