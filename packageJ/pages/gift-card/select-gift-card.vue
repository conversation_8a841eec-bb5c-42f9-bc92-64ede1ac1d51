<template>
  <view class="select-gift-card-container bg-ffffff">
    <!-- 导航栏 -->
    <view v-if="$vhFrom == '' || $vhFrom == 'next'" class=""> 
      <vh-navbar title="选择礼品卡" />
    </view>
    
    
    <!-- 卡片信息展示 -->
    <view class="card-display ptb-20-plr-24">
      <view class="card-preview bg-li-102">
        <!-- <text class="card-amount text-e80404 font-28">{{cardAmount}}元充值卡</text> -->
      </view>
      
      <!-- 数量选择 -->
      <view class="quantity-control flex-sb-c pt-20">
        <text class="text-e80404 font-28">{{cardAmount}}元充值卡</text>
        <view class="number-control flex-s-c">
          <view class="btn-minus flex-c-c" :class="quantity <= 1 ? 'btn-disabled' : ''" @click="decreaseQuantity">
            <text class="font-36">-</text>
          </view>
          <view class="quantity-input flex-c-c">
            <text class="text-3 font-28">{{quantity}}</text>
          </view>
          <view class="btn-plus flex-c-c" @click="increaseQuantity">
            <text class="font-36">+</text>
          </view>
        </view>
      </view>
      
      <!-- 提示信息 -->
      <view class="card-tips mt-20">
        <text class="font-24 text-9">充值卡金额不折现、不找零、不退换、全场可用（跨境、秒杀、挥泪清仓等特殊商品除外）</text>
      </view>
    </view>
    
    <!-- 分割线 -->
    <!-- <view class="divider"></view> -->
    
    <!-- 选择实体卡 -->
    <view class="physical-card-section ptb-20-plr-24">
      <!-- <view class="flex-sb-c mb-20">
        <view class="flex-s-c" @click="togglePhysicalCard">
          <view class="checkbox" :class="isPhysicalCard ? 'checkbox-checked' : ''">
            <view v-if="isPhysicalCard" class="checkbox-inner"></view>
          </view>
          <text class="ml-16 font-28 text-3">实物卡</text>
        </view>
      </view> -->
      
      <!-- 地址信息，仅在选择实体卡时显示 -->
      <view v-if="isPhysicalCard" class="address-info">
        <!-- 收件人信息 -->
        <view class="address-item ptb-16-plr-0 bb-s-01-eeeeee">
          <input 
            class="input-field font-28 text-3" 
            type="text" 
            placeholder="收件人" 
            v-model="addressInfo.consignee"
          />
        </view>
        
        <!-- 联系电话 -->
        <view class="address-item ptb-16-plr-0 bb-s-01-eeeeee">
          <input 
            class="input-field font-28 text-3" 
            type="number" 
            maxlength="11" 
            placeholder="联系电话" 
            v-model="addressInfo.consignee_phone"
          />
        </view>
        
        <!-- 所在地区 -->
        <view class="address-item ptb-16-plr-0 bb-s-01-eeeeee flex-sb-c" @click="openRegionPicker">
          <text class="font-28" :class="addressInfo.region ? 'text-3' : 'text-9'">
            {{addressInfo.region || '所在省市区'}}
          </text>
          <text class="font-28 text-9">></text>
        </view>
        
        <!-- 详细地址 -->
        <view class="address-item ptb-16-plr-0 bb-s-01-eeeeee">
          <input 
            class="input-field font-28 text-3" 
            type="text" 
            placeholder="详细地址" 
            v-model="addressInfo.address"
          />
        </view>
        
        <!-- 便于日后投递 -->
        <view class="address-item ptb-16-plr-0">
          <input 
            class="input-field font-28 text-3" 
            type="text" 
            placeholder="便于日后投递" 
            v-model="addressInfo.delivery_note"
          />
        </view>
        
        <!-- 从地址库选择按钮 -->
        <view class="select-from-address mt-16" @click="selectFromAddressList">
          <text class="font-28 text-3">从地址库选择</text>
        </view>
      </view>
    </view>
    
    <!-- 底部结算栏 -->
    <view class="bottom-bar flex-sb-c">
      <view class="price-section">
        <text class="font-28 text-3">合计：</text>
        <text class="font-32 text-e80404 font-wei">{{totalPrice.toFixed(2)}}元</text>
      </view>
      <view class="buy-button flex-c-c" @click="onBuyNow">
        <text class="font-28 text-ffffff">购买</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  data() {
    return {
      cardAmount: 200, // 礼品卡金额，默认200元
      cardId:0,
      quantity: 1, // 数量
      isPhysicalCard: false, // 是否为实体卡
      addressInfo: {
        consignee: '', // 收件人
        consignee_phone: '', // 联系电话
        region: '', // 所在地区
        province_id: '', // 省ID
        province_name: '', // 省名称
        city_id: '', // 市ID
        city_name: '', // 市名称
        town_id: '', // 区ID
        town_name: '', // 区名称
        address: '', // 详细地址
        delivery_note: '' // 便于日后投递
      }
    }
  },
  
  computed: {
    ...mapState(['routeTable', 'addressInfoState']),
    // 计算总价
    totalPrice() {
      return this.cardAmount * this.quantity;
    }
  },
  
  onLoad(options) {
    // 接收来自上一页传递的礼品卡金额数据
    if (options && options.amount) {
      this.cardAmount = parseFloat(options.amount);
      this.cardId = options.id;
    }
  },
  onShow () {
			if ( Object.keys(this.addressInfoState).length) {
				console.log('addressInfoState', this.addressInfoState)
				// const { province_id, province_name, city_id, city_name, town_id, town_name, address, consignee, consignee_phone } = this.addressInfoState
                this.setAddressInfo(this.addressInfoState);
			}
		},
  methods: {
    ...mapMutations(['muPayInfo']),
    // 增加数量
    increaseQuantity() {
      this.quantity++;
    },
    
    // 减少数量
    decreaseQuantity() {
      if (this.quantity > 1) {
        this.quantity--;
      }
    },
    
    // 切换是否选择实体卡
    togglePhysicalCard() {
      this.isPhysicalCard = !this.isPhysicalCard;
    },
    
    // 打开地区选择器
    openRegionPicker() {
      uni.showToast({
        title: '打开地区选择器',
        icon: 'none'
      });
      // 这里应该调用区域选择组件
    },
    
    // 从地址库选择地址
    selectFromAddressList() {
        this.jump.navigateTo(`${this.$routeTable.pEAddressManagement}?comeFrom=100`)
      
    },
    
    // 设置地址信息
    setAddressInfo(addressData) {
      if (!addressData) return;
      
      this.addressInfo = {
        consignee: addressData.consignee || '',
        consignee_phone: addressData.consignee_phone || '',
        region: `${addressData.province_name || ''} ${addressData.city_name || ''} ${addressData.town_name || ''}`,
        province_id: addressData.province_id || '',
        province_name: addressData.province_name || '',
        city_id: addressData.city_id || '',
        city_name: addressData.city_name || '',
        town_id: addressData.town_id || '',
        town_name: addressData.town_name || '',
        address: addressData.address || '',
        delivery_note: ''
      };
    },
    
    // 立即购买
    async onBuyNow() {
      // 验证地址信息
    //   if (this.isPhysicalCard) {
    //     if (!this.addressInfo.consignee) {
    //       return uni.showToast({
    //         title: '请填写收件人',
    //         icon: 'none'
    //       });
    //     }
    //     if (!this.addressInfo.consignee_phone) {
    //       return uni.showToast({
    //         title: '请填写联系电话',
    //         icon: 'none'
    //       });
    //     }
    //     if (!this.addressInfo.region) {
    //       return uni.showToast({
    //         title: '请选择所在地区',
    //         icon: 'none'
    //       });
    //     }
    //     if (!this.addressInfo.address) {
    //       return uni.showToast({
    //         title: '请填写详细地址',
    //         icon: 'none'
    //       });
    //     }
    //   }
      
      // 提交订单
    //   giftCardOrderCreate
     
      this.feedback.loading({ title: '正在下单...' })
      try {
        const data = {
            goods_id:this.cardId,
            order_from: this.$client,
            type:2,
            order_qty:this.quantity
        };
        let res = await this.$u.api.giftCardOrderCreate(data)
        let payInfo = {
          payPlate: 60,
          ...res.data, //下单信息
        }
        // uni.removeStorageSync('source') //下单成功后需要清除来源标识
        // this.muPayInfo(payInfo)
        if(this.$app){
          console.log('111111111111111-----');
            const appData = {
						paylmfor: payInfo,
						type: 60,
						priceString: payInfo.payment_amount,
						androidMainOrderNo: payInfo.order_no,
						androidFrom: '60'
					}
         
					this.jump.jumpAppPayment(this.$vhFrom, appData)
          if(this.$vhFrom != 'next'){
            wineYunJsBridge.openAppPage({
            client_path: { "ios_path":"finish", "android_path":"finish" }
           })
          }
          
        } else {
         
          
          this.muPayInfo(payInfo)
            this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
        }
        // this.jump.redirectTo(this.routeTable.pBPayment)
       
      } catch (e) {
        console.log(e)
        // setTimeout(() => {
        // 	this.jump.navigateBack()
        // }, 1500)
      }
     
    }
  }
}
</script>

<style scoped>
.select-gift-card-container {
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.card-display {
  width: 100%;
}

.card-preview {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.card-amount {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
}

.quantity-control {
  margin-top: 16rpx;
}

.number-control {
  display: flex;
  align-items: center;
}

.btn-minus, .btn-plus {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  background-color: #f8f8f8;
}

.btn-minus {
  border-radius: 6rpx 0 0 6rpx;
}

.btn-plus {
  border-radius: 0 6rpx 6rpx 0;
}

.btn-disabled {
  color: #ccc;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  border-top: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
}

.divider {
  height: 20rpx;
  background-color: #f5f5f5;
  margin: 20rpx 0;
}

.physical-card-section {
  margin-bottom: 100rpx;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 4rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox-checked {
  border-color: #E80404;
  background-color: #E80404;
}

.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 2rpx;
  background-color: #fff;
}

.address-info {
  padding: 10rpx 0 30rpx;
}

.address-item {
  width: 100%;
}

.input-field {
  width: 100%;
}

.select-from-address {
  padding: 20rpx 0;
  text-align: center;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  padding: 0 30rpx;
  z-index: 100;
}

.buy-button {
  width: 200rpx;
  height: 70rpx;
  background-color: #E80404;
  border-radius: 35rpx;
}
</style> 