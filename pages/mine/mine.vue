<template>
	<view class="content" :class="loading || showSignMask ? 'h-vh-100 o-hid' : ''">
		<!-- 用户数据 -->
		<view v-if="!loading" class="fade-in">
			<!-- 用户信息板块 -->
			<view class="">
				<!-- banner -->
				<image class="w-p100 h-508 p-abso" :src="`${osip}/mine/mine_bg.png`" mode="widthFix" />
				
				<!-- 用户信息 -->
				<view class="p-rela z-01 d-flex  pt-92 pl-40 j-sb">
					<view  class="d-flex a-center"> 
						<view class="p-rela w-98 h-98 bg-255-255-255-059 d-flex j-center a-center b-rad-p50"  @click="jump.appAndMiniJump(0, routeTable.pEUserInfo, from, 0, true)">
						<vh-image :src="userInfo.avatar_image" :loading-type="5" :width="90" :height="90" shape="circle" :duration="50" />
						<image v-if="userInfo.certified_info" class="p-abso right-0 bottom-0 w-38 h-38" :src="`${osip}/comm/lv_gold.png`" mode="aspectFill" />
					</view>
					
					<view class="ml-20">
						<view v-if="isLogin" class="">
							<view class="d-flex a-center">
								<view class="w-max-290 font-32 text-ffffff l-h-44 text-hidden">{{userInfo.nickname}}</view>
								<view class="flex-shrink flex-c-c ml-12 w-80 h-32 font-wei-600 font-22 text-ffffff bg-ff9127 b-rad-18" @click.stop="jump.appAndMiniJump(2, routeTable.pEMyGrade, from, 0, true)">
									<text class="p-rela" :class="sysPlatformAndroid ? 'top-n-02' : ''">LV.{{ userInfo.user_level }}</text>
								</view>
							</view>
							
							<view class="mt-10 d-flex a-center" @click.stop="jump.appAndMiniJump(0, userInfo.certified_info ? routeTable.pECertificationDetail : routeTable.pECertificationApply, from, 0, true)">
								<view class="bg-255-255-255-030 pl-18 pr-18 h-36 d-flex j-center a-center b-rad-22">
									<text class="mr-10 font-18 text-ffffff font-wei-500">{{userInfo.certified_info ? userInfo.certified_info : '申请认证'}}</text>
									<image :src="ossIcon('/mine/arrow_r_8_16.png')" class="w-08 h-16"></image>
								</view>
							</view>
						</view>
						
						<view v-else class="d-flex a-center" @click="loginClick">
							<view class="mr-10 font-36 font-wei text-ffffff">登录/注册</view>
							<u-icon name="arrow-right" :size="20" color="#FFF" />
						</view>
					</view>
					</view>
					
					<view class="mr-40" v-if="from == 'next'" @click="setClick">
						<image :src="ossIcon('/mine/mine_set_44.png')" class="w-44 h-44"/>
					</view>
				</view>
				
				<!-- 收藏、足迹、关注、粉丝 -->
				<view class="p-rela z-01 d-flex j-sa a-center pt-52">
					<view class="d-flex flex-column j-center a-center" v-for="(item, index) in numsList" :key="index" @click="jump.appAndMiniJump(0, item.page, from, 0, true)">
						<text class="l-h-44 text-ffffff font-wei font-32">{{item.nums}}</text>
						<text class="l-h-34 text-ffffff font-24">{{item.name}}</text>
					</view>
				</view>
				
			</view>
			
			<!-- 功能icon板块 -->
			<view class="p-rela z-01 mt-42">
				<!-- 板块1 兔头商店 + 幸运转盘 + 每日签到 + 我的订单 -->
				<view class="bg-ffffff b-rad-10 mr-24 mb-20 ml-24 ptb-00-plr-24">
					<!-- 兔头商店 + 幸运转盘 + 每日签到 -->
					<view class="ptb-30-plr-00 d-flex j-sb a-center">
						<view class="w-200 h-118 d-flex flex-column a-center b-rad-16"
						 :class="item.bgClass"
						v-for="(item, index) in rabResList" :key="index" @click="jump.appAndMiniJumpBD(0, item.page, 7, 3, 401000, index+1, from, 0, true)">
							<view class="mt-n-16">
								<vh-image :src="ossIcon(`/mine/mine_${item.icon}.png`)" :loading-type="5" :width="88" :height="80" mode="widthFix" />
							</view>
							<view class="d-flex a-center mt-04">
								<text class="mr-08 font-24 font-wei text-3 l-h-34">{{ item.title }}</text>
								<u-icon name="arrow-right" :size="20" :color="item.arrowColor" />
							</view>
						</view>
						
					</view>
					
					<!-- 我的订单 -->
					<view class="flex-sb-c ptb-30-plr-00 bt-s-01-eeeeee">
						<view class="d-flex flex-column j-center a-center" v-for="(item, index) in orderList" :key="index" @click="jumpOrder(item)">
							<view class="p-rela">
								<vh-image :src="`${osip}/mine/${item.icon}.png`" :loading-type="5" :width="48" :height="48" mode="widthFix" />
								<view v-if="item.nums > 0" class="p-abso top-n-10 right-n-14 w-30 h-30 bg-ff9127 d-flex j-center a-center b-rad-p50 font-20 text-ffffff">{{ item.nums | numMaxTwo}}</view>
								<!-- <image v-if="item.showNewLabel" class="p-abso top-n-10 right-n-10 w-26 h-22" :src="`${osip}/mine/new_label.png`"></image> -->
							</view>
							<view class="mt-08">
								<text class="text-9 font-24 l-h-40">{{item.name}}</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 板块2 兔头&优惠券 -->
				<view class="d-flex j-sb a-center mr-24 mb-20 ml-24">
					<view class="p-rela" @click="jump.appAndMiniJumpBD(0, routeTable.pEDailyTasks, 7, 3, 402000, 1, from, 0, true)">
						<image class="p-abso z-01 w-p100 h-p100" :src="`${osip}/mine/rab_bg.png`" mode="aspectFill"></image>
						<view class="p-rela z-02 d-flex flex-column a-center j-center w-222 h-148">
							<view>
								<image class="ml-18 w-30 h-30" :src="`${osip}/mine/rabbit.png`" mode="widthFix"></image>
								<text class=" text-6 font-30">兔头</text>
							 </view>
							
							<view class="ml-08 ">
								<!-- <view class="d-flex a-center">
									
									<text class="text-3 font-wei font-36 l-h-50">{{format.numberFormat(userInfo.rabbit)}}</text>
									<text class="ml-08 text-6 font-20 l-h-28">当前兔头</text>
								</view> -->
								<view class="">
									<text class="font-24 text-f15d22 l-h-34">{{userInfo.rabbit_available}}兔头未领取</text>
									<u-icon name="arrow-right" :size="20" color="#F15D22"></u-icon>
								</view>
							</view>
						</view>
					</view>
					
					<view class="p-rela" @click="jump.appAndMiniJumpBD(1, routeTable.pJMyBalance, 7, 3, 402000, 2, from, 0, true)">
						<image class="p-abso z-01 w-p100 h-p100" :src="`${osip}/mine/cou_bg.png`" mode="aspectFill"></image>
						<view class="p-rela z-02 d-flex a-center w-222 h-148">
							<!-- <image class="ml-18 w-106 h-106" :src="`${osip}/mine/coupon.png`" mode="widthFix"></image> -->
							<!-- <view class="ml-08 ">
								<view class="d-flex a-center">
									<text class="text-3 font-wei font-36 l-h-50">{{userInfo.coupon_totals}}</text>
									<text class="ml-08 text-6 font-20 l-h-28">张/优惠券</text>
								</view>
								<view class="">
									<text class="font-24 text-e81710 l-h-34">{{userInfo.conpon_expirings}}张即将过期</text>
									<u-icon name="arrow-right" :size="20" color="#E81710"></u-icon>
								</view>
							</view> -->
							<view class="p-rela z-02 d-flex flex-column a-center j-center w-226 h-148">
							<view>
								<image class="w-30 h-30" :src="`${osip}/mine/rabbit.png`" mode="widthFix"></image>
								<text class="ml-08 text-6 font-30 ">余额</text>
							 </view>
							
							<view class="ml-08 ">
							
								<view class="">
									<text class="font-24 text-f15d22 l-h-34">{{userInfo.rabbit_available}}余额测试</text>
									<u-icon name="arrow-right" :size="20" color="#F15D22"></u-icon>
								</view>
							</view>
						</view>
						</view>
					</view>
					<view class="p-rela" @click="jump.appAndMiniJumpBD(1, routeTable.pJGiftCard, 7, 3, 402000, 2, from, 0, true)">
						<image class="p-abso z-01 w-p100 h-p100" :src="`${osip}/mine/cou_bg.png`" mode="aspectFill"></image>
						<view class="p-rela z-02 d-flex a-center w-222 h-148">
							<!-- <image class="ml-18 w-106 h-106" :src="`${osip}/mine/coupon.png`" mode="widthFix"></image> -->
							<!-- <view class="ml-08 ">
								<view class="d-flex a-center">
									<text class="text-3 font-wei font-36 l-h-50">{{userInfo.coupon_totals}}</text>
									<text class="ml-08 text-6 font-20 l-h-28">张/优惠券</text>
								</view>
								<view class="">
									<text class="font-24 text-e81710 l-h-34">{{userInfo.conpon_expirings}}张即将过期</text>
									<u-icon name="arrow-right" :size="20" color="#E81710"></u-icon>
								</view>
							</view> -->
							<view class="p-rela z-02 d-flex flex-column a-center j-center w-226 h-148">
							<view>
								<image class="ml-18 w-30 h-30" :src="`${osip}/mine/rabbit.png`" mode="widthFix"></image>
								<text class=" text-6 font-30">充值卡</text>
							 </view>
							
							<view class="ml-08 ">
								
								<view class="">
									<text class="font-24 text-f15d22 l-h-34">{{userInfo.rabbit_available}}兔头未领取</text>
									<u-icon name="arrow-right" :size="20" color="#F15D22"></u-icon>
								</view>
							</view>
						</view>
						</view>
					</view>
				</view>
				
				<!-- 板块3 金刚区域1 -->
				<view v-if="goldFunList1.length" class="bg-ffffff d-flex flex-wrap b-rad-10 mr-24 mb-20 ml-24 pb-20 pl-18 pr-18">
					<view class="w-p20 d-flex flex-column j-center a-center mt-36" v-for="(item, index) in goldFunList1" :key="index" @click="jump.pubConfJumpBD(item, 7, 3, 403000, item.id, from)">
						<view class="p-rela">
							<vh-image :src="item.icon" :loading-type="5" :width="44" :height="44" mode="widthFix"></vh-image>
							<template>
								<view v-if="item.mineCustomNums" class="p-abso top-n-16 right-n-16 w-30 h-30 bg-ff9127 d-flex j-center a-center b-rad-p50 font-20 text-ffffff"> {{ item.mineCustomNums | numMaxTwo }}</view>
								<image v-else-if="item.badge" class="p-abso top-0 right-n-10 w-24 h-24" :src="item.badge" mode="aspectFill" />
							</template>
						</view>
						<text class="mt-10 font-24 text-9 l-h-40">{{item.label_name}}</text>
					</view>
				</view>
				
				<!-- 板块4 拍卖板块 -->
				<view v-if="false" class="bg-ffffff b-rad-10 mr-24 mb-20 ml-24 ptb-00-plr-24">
					<view class="flex-sb-c bb-s-02-eeeeee ptb-20-plr-00" @click="jump.loginNavigateTo(routeTable.pHAuctionCreditValuesFromMine)">
						<view class="font-32 text-3 font-wei-600 l-h-44">拍卖</view>
						<view class="bg-li-43 d-flex a-center ptb-00-plr-12 h-44 b-rad-60">
							<view class="font-wei-500 font-20 text-9e632a">{{ userInfo.auction_credit_score }}</view>
							<view class="ml-06 font-20 text-b68757">信用值</view>
							<view class="flex-c-c ml-02 w-24 h-24 b-rad-p50" style="background: #ffb100;">
								<image :src="ossIcon('/mine/arrow_r_12_22.png')" class="w-12 h-22"></image>
							</view>
						</view>
					</view>
					<view class="d-flex flex-wrap ptb-24-plr-00">
						<view
							v-for="(item, index) in auctionPlateList"
							:key="index"
							style="width: 33.33%"
							class="d-flex a-center"
							:class="[{ 'mt-32': index > 2 }, { 'pl-32': index % 3 === 1 }, { 'j-end': index % 3 === 2}]"
							@click="onAuctionJump(item)"
						>
							<view class="p-rela w-50 h-50">
								<vh-image :src="ossIcon(`/mine/auc_${item.icon}.png`)" :loading-type="5" :width="50" :height="50" mode="widthFix" />
								<view v-if="item.$nums > 0" class="p-abso top-n-16 right-n-04 w-30 h-30 bg-ff9127 d-flex j-center a-center b-rad-p50 font-20 text-ffffff">{{ item.$nums | numMaxTwo}}</view>
							</view>
							<view class="ml-04 font-24 text-9 l-h-40">{{ item.name }}</view>
						</view>
					</view>
				</view>
				
				<!-- 板块5 金刚区域2  -->
				<view v-if="goldFunList2.length" class="bg-ffffff d-flex flex-wrap b-rad-10 mr-24 mb-20 ml-24 pt-08 pb-20 pl-18 pr-18">
					<view class="w-p20 d-flex flex-column j-center a-center mt-28" v-for="(item, index) in goldFunList2" :key="index" @click="jump.pubConfJumpBD(item, 7, 3, 403000, item.id, from)">
						<view class="p-rela">
							<vh-image :src="item.icon" :loading-type="5" :width="74" :height="74" mode="widthFix"></vh-image>
							<template>
								<view v-if="item.mineCustomNums" class="p-abso top-n-10 right-n-14 w-30 h-30 bg-ff9127 d-flex j-center a-center b-rad-p50 font-20 text-ffffff"> {{ item.mineCustomNums | numMaxTwo }}</view>
								<image v-else-if="item.badge" class="p-abso top-0 right-n-10 w-24 h-24" :src="item.badge" mode="aspectFill" />
							</template>
						</view>
						<text class="mt-10 font-24 text-9 l-h-40">{{item.label_name}}</text>
					</view>
				</view>
				
				<!-- 板块6 金刚区域3 -->
				<view v-if="goldFunList3.length" class="bg-ffffff d-flex flex-wrap b-rad-10 mr-24 mb-20 ml-24 pt-08 pb-20 pl-18 pr-18">
					<view class="w-p20 d-flex flex-column j-center a-center mt-28" v-for="(item, index) in goldFunList3" :key="index" @click="jump.pubConfJumpBD(item, 7, 3, 403000, item.id, from)">
						<view class="p-rela">
							<vh-image :src="item.icon" :loading-type="5" :width="74" :height="74" mode="widthFix"></vh-image>
							<template>
								<view v-if="item.mineCustomNums" class="p-abso top-n-10 right-n-14 w-30 h-30 bg-ff9127 d-flex j-center a-center b-rad-p50 font-20 text-ffffff"> {{ item.mineCustomNums | numMaxTwo }}</view>
								<image v-else-if="item.badge" class="p-abso top-0 right-n-10 w-24 h-24" :src="item.badge" mode="aspectFill" />
							</template>
						</view>
						<text class="mt-10 font-24 text-9 l-h-40">{{item.label_name}}</text>
					</view>
				</view>
				
			</view>
			
			<!-- 弹框 -->
			<view v-if="Object.keys(signInfo).length" class="">
				<!-- 签到遮罩层 -->
				<u-mask :show="showSignMask" :zoom="false" @click="closeSignMask()">
					<view class="h-p100 d-flex j-center a-center">
						<view v-if="signInfo.signin_record.status == 0" class="p-rela z-01 w-506 h-772 mb-20" @click.stop>
							<image class="p-abso z-01 w-p100 h-p100" :src="`${osip}/mine/sign_eve_bg.png`" mode="aspectFill"></image>
							
							<view class="p-rela z-02 w-p100 h-p100">
								<view class="w-p100 h-172 d-flex flex-column j-center a-center">
									<view class="mt-20 font-38 font-wei text-ffffff l-h-52">每日签到</view>
									<view class="mt-06 font-24 text-ffffff l-h-34">{{signInfo.signin_record.signin_desc}}</view>
								</view>
								
								<view class="w-p100 h-600 d-flex flex-column  a-center">
									<view class="d-flex flex-wrap j-center a-center">
										<view class="d-flex flex-column j-center a-center mt-32 ml-08 mr-08" v-for="(item, index) in signInfo.signin" :key = "index">
											<view class="w-96 h-126 b-rad-06 o-hid">
												<view class="w-p100 h-34 d-flex j-center a-center font-24 text-ffffff" 
												:class="item.days <= signInfo.signin_record.days ? item.days == signInfo.signin_record.days && !signInfo.signin_record.status ? 'bg-e80404' :'bg-ffa825' : 'bg-9f9f9f'">{{ item.days == signInfo.signin_record.days && !signInfo.signin_record.status  ? `今天` : `第${item.days}天`}}</view>
												<view class="w-p100 h-92 d-flex j-center a-center" 
												:class="item.days <= signInfo.signin_record.days ? item.days == signInfo.signin_record.days && !signInfo.signin_record.status ? 'bg-ffeaea' :'bg-fdf3ef' : 'bg-f6f6f6'">
													<image class="w-46 h-48"
													:src="`${osip}/comm/${item.days <= signInfo.signin_record.days ? item.days == signInfo.signin_record.days && !signInfo.signin_record.status ? 'rab_gold' : 'signed_gold' : 'rab_gray' }.png`" 
													mode="aspectFill" />
												</view>
											</view>
											<view class="mt-04 font-22 text-9 l-h-32">
												+{{item.rabbit}}兔头
											</view>
										</view>
									</view>
									
									<view class="d-flex j-center a-center mt-36">
										<view class="w-70 h-01 bg-f8a233 t-sc-y-h-1" />
										<view class="ml-14 mr-14 font-22 text-f8a233">连续签到{{signInfo.signin_record.days - 1}}天</view>
										<view class="w-70 h-01 bg-f8a233 t-sc-y-h-1" />
									</view>
									
									<view class="d-flex j-center a-center mt-24">
										<view class="">
											<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
											:custom-style="{width:'402rpx', height:'64rpx', fontSize:'30rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="signInNow">立即签到</u-button>
										</view>
									</view>
								</view>
							</view>
						</view>
						
						<view v-else class="p-rela z-01 w-506 h-772 mb-20" @click.stop>
							<image class="p-abso z-01 w-p100 h-p100" :src="`${osip}/mine/sign_eve_bg.png`" mode="aspectFill"></image>
							
							<view class="p-rela z-02 w-p100 h-p100">
								<view class="w-p100 h-172 d-flex flex-column j-center a-center">
									<view class="mt-20 font-40 font-wei text-ffffff">{{ signInfo.signin_record.days < 7 ? '签到成功' : '满签成功' }}</view>
								</view>
								
								<view class="w-p100 h-600 d-flex flex-column a-center">
									<view class="mt-32">
										<image class="w-442 h-296" :src="`${osip}/mine/sign_suc.png`" mode="aspectFill"></image>
										
										
									</view>
									
									<view class="d-flex j-center mt-44">
										<view class="w-348 text-center font-30 text-3 l-h-42">
											恭喜您，您已连续签到<text class="text-f8a233">{{signInfo.signin_record.days}}</text>天 
											获得{{signInfo.signin_record.days == 7 ? '额外' : ''}}<text class="text-f8a233">+{{signInfo.signin[signInfo.signin_record.days-1].rabbit}}</text>兔头！
										</view>
									</view>
									
									<view class="d-flex j-center a-center mt-30">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'402rpx', height:'64rpx', fontSize:'30rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="jumpDailyTasks">赚取更多兔头</u-button>
									</view>
								</view>
							</view>
						</view>
					</view>
				</u-mask>
			</view>
			
			<NewPeopleFloatingFrame
				v-if="newPeopleFloatingFrameVisible" 
				:isLogin="userIsLogin"
				:received="newPeopleCouponPackage.collect_status" 
				@jumpActivity="onJumpActivityPage"
			/>

			<AuctionRNWarnPopup v-model="auctionRNWarnPopupVisible"></AuctionRNWarnPopup>
			<AuctionPubCategorySelectPopup v-model="auctionPubCategorySelectPopupVisible"></AuctionPubCategorySelectPopup>
		</view>
	    
		<!-- 加载状态 -->
	    <vh-skeleton v-else :has-status-bar="false" :has-navigation-bar="false" :has-tab-bar="true" :show-loading="false" />
		
		<!-- 底部导航栏 tabBar -->
		<vh-tabbar  v-if="from == ''"  id="index-tabbar"
		tabbarClass="grayscale-100" :loading="loading" @topRefresh="topRefresh"/>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	import auctionPlateList from '@/common/js/data/mine/auctionPlateList.js'
	import newPeopleMixin from '@/common/js/mixins/newPeopleMixin'
	import userMixin from '@/common/js/mixins/userMixin'
	import topRefreshMixin from '@/common/js/mixins/topRefreshMixin'
	import startupPageOptionsMixin from '@/common/js/mixins/startupPageOptionsMixin'
	import { MAuctionGoldAreaJumpPage } from '@/common/js/mapper/mine/model'

	export default{
		mixins: [newPeopleMixin, userMixin, topRefreshMixin, startupPageOptionsMixin],
		name: 'mine',
		data() {
			return {
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				loading: true, //加载状态
				isLogin: false, //是否登录
				userInfo: {
					rabbit: 0, //当前兔头
					rabbit_available: 0, //未领取兔头
					coupon_totals: 0, //总优惠券
					conpon_expirings: 0, //即将过期的优惠券
					auction_credit_score: 100, //信用值
					// avatar_image:'',
					// certified_info:'',
					// avatar_image:'',
					// avatar_image:'',
					// avatar_image:'',
					// avatar_image:'',
					// avatar_image:'',
				}, //用户数据
				numsList: [ // 收藏、关注、粉丝列表
					{ 
						name: '收藏', 
						nums: 0 ,
						page: '/packageE/pages/my-collection/my-collection',
					},
					{
						name: '足迹', 
						nums: 0 ,
						page: '/packageE/pages/my-footprint/my-footprint',
					},
					{ 
						name: '关注', 
						nums: 0 ,
						page: '/packageE/pages/my-attention-list/my-attention-list',
					},
					{ 
						name: '粉丝', 
						nums: 0 ,
						page: '/packageE/pages/my-fans-list/my-fans-list',
					},
				], 
				rabResList: [ //兔头商店列表
					{
					   icon:'rabbit',
					   title:'兔头商店',
					   page:'/packageB/pages/rabbit-head-shop/rabbit-head-shop',
					   bgClass: 'bg-li-35',
					   arrowColor: '#FF9127'
					},
					{
					    icon:'draw',
						title:'幸运转盘',
						page:'/packageE/pages/large-turntable/large-turntable',
						bgClass: 'bg-li-36',
						arrowColor: '#52A2FF'
					},
					{
						icon:'sign',
						title:'每日签到',
						page:'/packageE/pages/daily-tasks/daily-tasks',
						bgClass: 'bg-li-37',
						arrowColor: '#FC699D'
					}
				],
				orderList: [ //订单列表
					{
						icon:'order_pay',
						name:'待支付',
						nums:0,
						status:1
					},
					{
						icon:'order_deliver',
						name:'待发货',
						nums:0,
						status:3
					},
					{
						icon:'order_get',
						name:'待收货',
						nums:0,
						status:4
					},
					{
						icon:'order_wine_comm_2',
						name:'写酒评',
						nums:0,
						status:5,
					},
					{
						icon:'order_all',
						name:'全部订单',
						nums:0,
						status:0
					},
				],
				auctionPlateList, //拍卖板块列表
				goldFunList1: [], //金刚区功能列表1
				goldFunList2: [], //金刚区功能列表2
				goldFunList3: [], //金刚区功能列表3
				signInfo: {}, //日常任务信息
			    showSignMask: false, //是否展示签到遮罩层
				auctionRNWarnPopupVisible: false,
				auctionPubCategorySelectPopupVisible: false,
				from:'',
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['isCancelMineSignMask', 'routeTable', 'couponExpirationReminder']),
			sysPlatformAndroid () {
				return this.system.sysPlatformAndroid()
			},
		},
		
		onLoad(options) {
			
			
			if (options.from && options.version) {
			
				this.from = options.from
				console.log('123--------', this.from);
				this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
				// this.muVersion(options.version) //记录app版本号
				window.indexTopRefresh = () => {
					this.topRefresh()
				}
				}
				this.init()
			this.system.setNavigationBarBlack()
		},
		
		onShow() {
			
			this.login.isLoginV3(this.$vhFrom, 0).then(isLogin => {
				console.log('islgin------', isLogin);
				
				this.userIsLogin = isLogin;
				this.isLogin = isLogin;
				if(isLogin && this.from == 'next'){
					console.log('userIsLogin------', this.userIsLogin);
					this.init();
				}
				if(!isLogin){
					this.numsList = this.$options.data().numsList;
					this.orderList = this.$options.data().orderList;
					this.userInfo = this.$options.data().userInfo;
				}
			})
			
		},
		
		methods: {
			
			// Vuex mapMutations辅助函数
			...mapMutations(['muUserInfo', 'muIsCancelMineSignMask', 'muCouponExpirationReminder', 'muFrom', 'muVersion']),
			
			// 初始化（轮播...）
			async init() {
				this.showTabBar()
				try{
					if(this.login.isLogin('', 0)){
						await Promise.all([ this.getUserData(), this.getDailyTasksList() ])
						this.loading = false
						this.isLogin = true
						// 判断是否需要弹出签到弹框
						if(this.signInfo.signin_record.status == 0 && this.isCancelMineSignMask == 0) {
							this.showSignMask = true
						}
					}else{
						this.loading = false
					}
					await this.getFuncList()
					uni.stopPullDownRefresh()
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 获取用户信息
			async getUserData(){
				
				let res = await this.$u.api.userData()
				this.userInfo = res.data
				// this.userInfo.avatar_image = this.userInfo.avatar_image + `&param=1`
				console.log(JSON.stringify(this.userInfo))
				uni.setStorageSync('UserInfo',res.data);
				this.muUserInfo(res.data)
				this.muCouponExpirationReminder( res.data.is_pushcoubtn == '0' ? false : true )
				this.getNums()
				this.getOrderStatusNums()
				this.getAuctionNums()
			},
			
			// 获取日常任务列表
			async getDailyTasksList(){
				let res = await this.$u.api.dailyTasksList()
				this.signInfo = res.data
			},
			
			// 获取个功能区数据
			async getFuncList() {
				const { after_sale_nums } = this.userInfo
				let { data: { list } } = await this.$u.api.mineFuncList({ client: this.from == 'next' ? 5 : 3, channel: 5 })
				if( after_sale_nums > 0 ) {
					list.forEach(item => {
						if( item?.client_path?.code === 'AfterOrder') item.mineCustomNums = after_sale_nums
					})
				}
				this.goldFunList1 = list.filter(item => { return item.page_area === 1 }) //金刚区1
				this.goldFunList2 = list.filter(item => { return item.page_area === 2}) //金刚区2
				this.goldFunList3 = list.filter(item => { return item.page_area === 3 }) //金刚区3

			},
			
			// 立即签到
			async signInNow(){
				let res = await this.$u.api.signIn()
				this.signInfo.signin_record.status = 1
				this.feedback.toast({title:'签到成功', icon:'success'})
			},
			 // 是否显示底部tabBar
			 showTabBar() {
				console.log('222-------',this.from);
				
				if (this.from) {
					console.log('hide------------------event');
					
					uni.hideTabBar()
				}
			},
			// 关闭签到遮罩层
			closeSignMask() {
				console.log('--------我是取消签到遮罩')
				this.muIsCancelMineSignMask(1)
				this.showSignMask = false
			},
			
			// 获取数量（收藏、足迹、关注、粉丝）
			getNums(){
				const { collect_nums, footprint_nums, attention_nums, fan_nums } = this.userInfo //用户信息
				for(let i = 0; i < 4; i++){
					switch(i){
						case 0:
						this.numsList[i].nums = collect_nums //收藏数量
						break
						case 1:
						this.numsList[i].nums = footprint_nums //足迹数量
						break
						case 2:
						this.numsList[i].nums = attention_nums //关注数量
						break
						case 3:
						this.numsList[i].nums = fan_nums //粉丝数量
						break
					}
				}
			},
			
			// 获取订单状态数量（待支付、待发货、待收货、）
			getOrderStatusNums(){
				const { unpaid_nums, unporder_nums, paid_nums, shipped_nums, after_sale_nums, wine_comment_nums } = this.userInfo //用户信息
				for(let i = 0; i < 3; i++){
					switch(i){
						case 0:
						this.orderList[i].nums = unpaid_nums //待支付数量
						break
						case 1:
						this.orderList[i].nums = paid_nums //待发货数量
						break
						case 2:
						this.orderList[i].nums = shipped_nums //待收货数量
						break
					}
				}
				this.orderList[3].nums = wine_comment_nums
			},
			
			// 获取拍卖数量
			getAuctionNums(){
				const { auction_reminder_nums } = this.userInfo
				this.auctionPlateList = this.auctionPlateList.map(item => {
					if (item.page === MAuctionGoldAreaJumpPage.Remind) item.$nums = auction_reminder_nums
					return item
				})
			},
			 // login
			 loginClick() {
				if (this.comes.isFromApp(this.from)) {
					wineYunJsBridge.openAppPage({
						client_path: { ios_path: 'login', android_path: 'login' },
					})
				} else {
					this.jump.loginNavigateTo( this.userInfo.certified_info ? this.routeTable.pECertificationDetail : this.routeTable.pECertificationApply )
				}
			},
			
			// 跳转订单 item = 订单列表某一项
			jumpOrder({ status }) {
				
				if (this.comes.isFromApp(this.from)) {
					if( status === 5 ) { //跳转酒评
						this.jump.appAndMiniJumpBD(0, this.routeTable.pCWineComment, 7, 3, 701000, 2, this.from, 0, true)
						
					}else { //跳转订单列表
						this.jump.appAndMiniJump(0, `${this.routeTable.pEMyOrder}?status=${status}`, this.from, 0, true)
					}
				} else {
					if( status === 5 ) { //跳转酒评
						this.jump.loginNavigateToBD(this.routeTable.pCWineComment, 7, 3, 701000, 2)
					}else { //跳转订单列表
						this.jump.loginNavigateTo(`${this.routeTable.pEMyOrder}?status=${status}`)
					}
				}
				
			},
		    
			// 跳转日常任务页面
			jumpDailyTasks(){
				this.showSignMask = false
				this.jump.appAndMiniJump(0, this.routeTable.pEDailyTasks, this.from)
				// this.jump.navigateTo(this.routeTable.pEDailyTasks)
			},
			setClick(){
				wineYunJsBridge.openAppPage({
						client_path: {
							android_path: 'com.stg.rouge.activity.SettingActivity',
						},
						ad_path_param: [
								{ 
									"android_key":"login", "android_val":"1" 
								}
							]
            		})
				
			},
			onAuctionJump (item) {
				if (item.page === 'pHAuctionGoodsPublish') {
					if (this.userInfo.is_auction_seller == 1) {
						this.auctionPubCategorySelectPopupVisible = true
					} else {
						this.auctionRNWarnPopupVisible = true
					}
					return
				}
				this.jump.loginNavigateTo(this.routeTable[item.page])
			}
		},
	    
		onPullDownRefresh() {
			this.init()
		},
	}
</script>

<style>
	@import "../../common/css/page.css";
</style>

<style lang="scss" scoped>
	.nick-name {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>
