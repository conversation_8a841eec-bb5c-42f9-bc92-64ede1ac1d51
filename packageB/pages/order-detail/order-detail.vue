<template>
  <view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
    <!-- 数据加载完成 -->
    <view v-if="!loading" class="fade-in bg-f5f5f5 pb-104">
      <!-- 导航栏 -->
      <vh-navbar
        back-icon-color="#FFF"
        title="订单详情"
        title-color="#FFF"
        :background="{ background: navBackgroundColor }"
      />

      <!-- banner -->
      <image
        class="p-abso z-0 top-0 w-p100 h-400"
        src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ban.png"
        mode="aspectFill"
      />

      <!-- 状态（待付款、待收货、拼团中...） -->
      <view class="p-rela z-01 mt-n-20 ml-48 mr-48">
        <!-- 拼团订单状态 -->
        <view v-if="getSpellStatus" class="">
          <!-- 拼团中 -->
          <view v-if="getSpellStatus == 1" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">拼团中</view>
              <view class="mt-08">
                <text class="font-28 text-ffffff">已付款：¥{{ orderDetailInfo.payment_amount }}</text>
                <text class="ml-24 font-28 text-ffffff">剩余：{{ orderDetailInfo.groupInfo.remaining_time }}</text>
              </view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_pro_ing.png"
              mode="aspectFill"
            />
          </view>

          <!-- 拼团失败 -->
          <view v-if="getSpellStatus == 3" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">拼团失败</view>
              <view class="mt-08">
                <text class="font-28 text-ffffff">已付款：¥{{ orderDetailInfo.payment_amount }}</text>
                <text class="ml-24 font-28 text-ffffff">剩余：{{ orderDetailInfo.groupInfo.remaining_time }}</text>
              </view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_clo.png"
              mode="aspectFill"
            ></image>
          </view>
        </view>

        <!-- 普通订单状态 -->
        <view v-else class="">
          <!-- 待付款 -->
          <view v-if="orderDetailInfo.status == 0" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">待付款</view>
              <view class="d-flex a-center mt-08">
                <view class="font-28 text-ffffff">需付款：¥{{ orderDetailInfo.payment_amount }}</view>
                <view class="d-flex a-center ml-24">
                  <view class="font-28 text-ffffff">剩余：</view>
                  <!-- 倒计时结束后不请求接口。马上将状态切换为交易关闭 （orderDetailInfo.status = 4） -->
                  <vh-count-down
                    :show-days="false"
                    :showHours="false"
                    :timestamp="orderDetailInfo.countdown"
                    separator="zh"
                    bg-color="transparent"
                    separator-color="#fff"
                    @end="orderDetailInfo.countdown = 0"
                  ></vh-count-down>
                </view>
              </view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_be_pad.png"
              mode="aspectFill"
            />
          </view>

          <!-- 待发货 -->
          <view v-if="orderDetailInfo.status == 1" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">待发货</view>
              <view class="mt-08 font-28 text-ffffff">买家已付款，等待商家发货</view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_be_ship.png"
              mode="aspectFill"
            />
          </view>

          <!-- 待收货 -->
          <view v-if="orderDetailInfo.status == 2" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">待收货</view>
              <view class="mt-08 font-28 text-ffffff">自动收货时间：{{ orderDetailInfo.receipt_time }}</view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_be_rec.png"
              mode="aspectFill"
            />
          </view>

          <!-- 已完成 -->
          <view v-if="orderDetailInfo.status == 3" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">已完成</view>
              <view class="mt-08 font-28 text-ffffff">订单已完成，感谢您对酒云网的支持！</view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_comp.png"
              mode="aspectFill"
            ></image>
          </view>

          <!-- 交易关闭 -->
          <view v-if="orderDetailInfo.status == 4" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">交易关闭</view>
              <view class="mt-08">
                <text class="font-28 text-ffffff">需付款：¥{{ orderDetailInfo.payment_amount }}</text>
                <text class="ml-24 font-28 text-ffffff">剩余：0分钟0秒</text>
              </view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_clo.png"
              mode="aspectFill"
            ></image>
          </view>

          <!-- 已暂存 -->
          <view v-if="orderDetailInfo.status == 6" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">已暂存</view>
              <view class="mt-08 font-28 text-ffffff">预计发货时间：{{ orderDetailInfo.predict_time }}</view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_staged.png"
              mode="aspectFill"
            />
          </view>

          <!-- 处理中 -->
          <view v-if="orderDetailInfo.status == 7" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">处理中</view>
              <view class="mt-08 font-28 text-ffffff">工作人员会在7个工作日内退款</view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_pro_ing.png"
              mode="aspectFill"
            ></image>
          </view>

          <!-- 退款成功 -->
          <view v-if="orderDetailInfo.status == 8" class="d-flex j-sb a-center">
            <view class="">
              <view class="font-36 font-wei text-ffffff">退款成功</view>
              <view class="mt-08">
                <text class="font-28 text-ffffff">需付款：¥{{ orderDetailInfo.payment_amount }}</text>
                <text class="ml-24 font-28 text-ffffff">剩余：0分钟0秒</text>
              </view>
            </view>
            <image
              class="w-128 h-128"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_clo.png"
              mode="aspectFill"
            ></image>
          </view>
        </view>
      </view>

      <!-- 退货、退款审核 -->
      <view class="">
        <!-- 拼团失败 -->
        <view v-if="getSpellStatus == 3" class="p-rela z-01 bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
            <view class="d-flex">
              <image
                class="w-44 h-44"
                src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ref_bla.png"
                mode="widthFix"
              ></image>
              <view class="ml-08">
                <view class="font-32 font-wei text-3">{{ orderDetailInfo.groupInfo.refund_schedule[0].title }}</view>
                <view class="mt-12 font-24 text-9 l-h-34">{{ orderDetailInfo.groupInfo.refund_schedule[0].time }}</view>
              </view>
            </view>
            <u-icon name="arrow-right" :size="24" color="#333" />
          </view>

          <view class="pt-32 pb-32 ml-60">
            <view class="font-28 text-3"
              >退款金额：<text class="text-e80404">¥{{ orderDetailInfo.payment_amount }}</text></view
            >
          </view>
        </view>

        <!-- 普通商品 -->
        <view v-if="false" class="p-rela z-01 bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
            <view class="d-flex">
              <image
                class="w-44 h-44"
                src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ref_bla.png"
                mode="widthFix"
              ></image>
              <view class="ml-08">
                <view class="font-32 font-wei text-3">退货/退款审核中</view>
                <view class="mt-12 font-24 text-9 l-h-34">2021.03.05 14:28</view>
              </view>
            </view>
            <u-icon name="arrow-right" :size="24" color="#333" />
          </view>

          <view class="pt-32 pb-32 ml-60">
            <view class="font-28 text-3"
              >退款金额：<text class="text-e80404">¥{{ orderDetailInfo.payment_amount }}</text></view
            >
          </view>
        </view>
      </view>

      <!-- 收货地址 -->
      <view v-if="getSpellStatus != 3" class="p-rela z-01 bg-ffffff b-rad-10 mt-64 ml-24 mr-24 ptb-00-plr-24">
        <!-- 待收货、已完成需要显示物流信息 -->
        <view
          v-if="
            orderDetailInfo.status == 2 ||
            orderDetailInfo.status == 3 ||
            (orderDetailInfo.status == 1 && (orderDetailInfo.order_type === 1 || orderDetailInfo.order_type === 3))
          "
          class=""
        >
          <!-- 待收货且拥有物流轨迹 -->
          <view
            v-if="
              (orderDetailInfo.status == 2 || orderDetailInfo.status == 1) &&
              logisticInfo.traces &&
              logisticInfo.traces.length
            "
            class="d-flex bb-s-01-eeeeee ptb-32-plr-00"
            @click="viewLogistics()"
          >
            <image
              class="w-44 h-44 mt-10"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/rec_bla.png"
              mode="widthFix"
            />
            <view class="flex-1 d-flex j-sb a-center ml-16">
              <view class="w-540">
                <view class="font-32 font-wei text-3 text-hidden-3">{{ logisticInfo.traces[0].context }}</view>
                <view class="mt-12 font-24 text-3 l-h-34">{{ logisticInfo.traces[0].ftime }}</view>
              </view>
              <u-icon name="arrow-right" :size="24" color="#333" />
            </view>
          </view>
          <!-- 已完成 -->
          <view
            v-else-if="orderDetailInfo.status == 3"
            class="d-flex bb-s-01-eeeeee ptb-32-plr-00"
            @click="viewLogistics()"
          >
            <image
              class="w-44 h-44 mt-10"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/rec_bla.png"
              mode="widthFix"
            />
            <view class="flex-1 d-flex j-sb a-center ml-16">
              <view class="w-540">
                <view class="font-32 font-wei text-3">已签收</view>
                <view class="mt-12 font-24 text-3 l-h-34">{{ orderDetailInfo.goods_receipt_time }}</view>
              </view>
              <u-icon name="arrow-right" :size="24" color="#333" />
            </view>
          </view>
          <!-- 暂无物流轨迹 -->
          <view v-else class="d-flex bb-s-01-eeeeee ptb-32-plr-00" @click="viewLogistics()">
            <image
              class="w-44 h-44 mt-10"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/rec_bla.png"
              mode="widthFix"
            />
            <view class="flex-1 d-flex j-sb a-center ml-16">
              <view class="w-540">
                <view class="font-32 font-wei text-3">暂无物流轨迹信息</view>
                <view class="mt-12 font-24 text-3 l-h-34">{{ $u.timeFormat(date.getTimeStamp(), 'yyyy-mm-dd') }}</view>
              </view>
              <u-icon name="arrow-right" :size="24" color="#333" />
            </view>
          </view>
        </view>
        <view class="p-rela d-flex ptb-32-plr-00">
          <image
            class="w-44 h-44 mt-04"
            src="https://images.vinehoo.com/vinehoomini/v3/order_detail/add_bla.png"
            mode="widthFix"
          />
          <view class="w-540 ml-16">
            <view class="">
              <text class="mr-36 font-32 font-wei text-3">{{ orderDetailInfo.consignee }}</text>
              <text class="font-28 text-3">{{ orderDetailInfo.consignee_phone }}</text>
            </view>
            <view class="mt-12 font-24 text-3 l-h-34"
              >{{ orderDetailInfo.province_name }} {{ orderDetailInfo.city_name }} {{ orderDetailInfo.district_name }}
              {{ orderDetailInfo.address }}</view
            >
          </view>
          <button
            v-if="[1, 6].includes(orderDetailInfo.status)"
            style="top: 50%; right: 0; transform: translateY(-50%)"
            class="p-abso vh-btn flex-c-c w-108 h-52 font-26 text-6 b-rad-26 bg-ffffff b-s-01-d8d8d8"
            @click="oaucPopupVisible = true"
          >
            修改
          </button>
        </view>
      </view>

      <!-- 拼团 -->
      <view
        v-if="getSpellStatus == 1 || getSpellStatus == 3"
        class="p-rela z-01 bg-ffffff b-rad-10 d-flex flex-column a-center j-center mt-20 ml-24 mr-24 pt-32 pb-32"
      >
        <view v-if="getSpellStatus == 1" class="font-28 font-wei text-3"
          >拼团中，还差<text class="text-e80404">{{ orderDetailInfo.groupInfo.group_last_num }}人</text>即可拼成</view
        >
        <view v-if="getSpellStatus == 3" class="font-28 font-wei text-3">哎呀，拼团时间已过，人数不够！</view>

        <view
          class="w-648 d-flex a-center mt-40 o-scr-x"
          :class="orderDetailInfo.groupInfo.user_head_img.length > 5 ? '' : 'j-center'"
        >
          <view
            class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 mr-n-20"
            :style="{ zIndex: orderDetailInfo.groupInfo.user_head_img.length - index }"
            v-for="(item, index) in orderDetailInfo.groupInfo.user_head_img"
            :key="index"
          >
            <image class="w-108 h-108 b-rad-p50" :src="item" mode="aspectFill" />
            <view v-if="index == 0" class="p-abso z-03 bottom-0 w-110 d-flex j-center">
              <text class="bg-ff9127 b-rad-16 b-s-02-ffffff ptb-02-plr-16 font-20 text-ffffff">团长</text>
            </view>
          </view>

          <view class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 ml-54">
            <image
              class="w-108 h-108 b-rad-p50"
              src="https://images.vinehoo.com/vinehoomini/v3/order_detail/que_mark.png"
              mode="aspectFill"
            />
          </view>
        </view>

        <view class="mt-50 d-flex j-center a-center">
          <view v-if="getSpellStatus == 1" class="">
            <u-button
              shape="circle"
              @click="inviteGroup"
              :hair-line="false"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '546rpx',
                height: '64rpx',
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#FFF',
                backgroundColor: '#E80404',
                border: 'none',
              }"
              open-type="share"
              >邀请好友拼团</u-button
            >
          </view>
          <view v-if="getSpellStatus == 3" class="">
            <u-button
              shape="circle"
              :hair-line="false"
              @click="inviteGroup"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '546rpx',
                height: '64rpx',
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#FFF',
                backgroundColor: '#DDDDDD',
                border: 'none',
              }"
              >邀请好友拼团</u-button
            >
          </view>
        </view>

        <view class="mt-32 font-24 text-9">拼团时长超出24小时会被取消</view>
      </view>

      <!-- 订单列表 -->
      <view class="p-rela z-01 bg-ffffff b-rad-10 mt-20 mr-24 ml-24 pt-24">
        <view
          class="pr-20 pb-24 pl-28"
          v-for="(item, index) in orderDetailInfo.goodsInfo"
          :key="index"
          @click="goodsClick(item)"
        >
          <view class="d-flex j-sb">
            <OrderGoodsImage :orderType="orderDetailInfo.order_type" :goodsImg="item.goods_img" />

            <view class="flex-1 d-flex flex-column j-sb ml-12">
              <OrderGoodsTitle :goodsInfo="item" />
              <view class="">
                <view class="">
                  <OrderGoodsTag :orderType="orderDetailInfo.order_type" :goodsInfo="item" />
                </view>
                <view class="mt-04 d-flex j-sb a-end">
                  <text class="font-22 text-6">x{{ item.order_qty }}</text>
                  <text class="font-28 text-3">¥{{ item.package_price }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 订单明细 -->
      <view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">订单编号</text>
          <text class="font-24 text-3" @click.stop="copy.copyText(orderDetailInfo.order_no)">{{
            orderDetailInfo.order_no
          }}</text>
        </view>

        <template v-if="![0, 4].includes(orderDetailInfo.status)">
          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
            <text class="font-28 font-wei text-3">配送方式</text>
            <text class="font-24 text-3">{{ orderDetailInfo.express_name }}</text>
          </view>

          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
            <text class="font-28 font-wei text-3">支付方式</text>
            <text v-if="orderDetailInfo.payment_method == 0" class="font-24 text-3">支付宝APP</text>
            <text v-else-if="orderDetailInfo.payment_method == 1" class="font-24 text-3">支付宝H5</text>
            <text v-else-if="orderDetailInfo.payment_method == 2" class="font-24 text-3">支付宝PC</text>
            <text v-else-if="orderDetailInfo.payment_method == 3" class="font-24 text-3">微信APP</text>
            <text v-else-if="orderDetailInfo.payment_method == 4" class="font-24 text-3">微信小程序</text>
            <text v-else-if="orderDetailInfo.payment_method == 5" class="font-24 text-3">微信H5</text>
            <text v-else-if="orderDetailInfo.payment_method == 6" class="font-24 text-3">抖音支付宝</text>
            <text v-else-if="orderDetailInfo.payment_method == 7" class="font-24 text-3">微信JSAPI(公众号支付)</text>
            <text v-else-if="orderDetailInfo.payment_method == 8" class="font-24 text-3">抖音微信</text>
            <text v-else-if="orderDetailInfo.payment_method == 201" class="font-24 text-3">兔头</text>
            <text v-else-if="orderDetailInfo.payment_method == 202" class="font-24 text-3">礼品卡</text>
            <text v-else class="font-24 text-3">未知</text>
          </view>

          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
            <text class="font-28 font-wei text-3">预计发货时间</text>
            <text class="font-24 text-3">{{ orderDetailInfo.predict_time }}前</text>
          </view>
        </template>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">下单时间</text>
          <text class="font-24 text-3">{{ orderDetailInfo.created_time }}</text>
        </view>

        <view
          v-if="![0, 4].includes(orderDetailInfo.status)"
          class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"
        >
          <text class="font-28 font-wei text-3">支付时间</text>
          <text class="font-24 text-3">{{ orderDetailInfo.payment_time }}</text>
        </view>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">发票信息</text>
          <text
            v-if="
              orderDetailInfo.order_type !== 2 &&
              !orderDetailInfo.invoice_progress &&
              [1, 2, 6].includes(orderDetailInfo.status)
            "
            class="font-24 text-2e7bff"
            @click="onJumpInvoiceManagement"
            >申请开票</text
          >
          <text v-else-if="!orderDetailInfo.invoice_progress" class="font-24 text-3">不开发票</text>
          <view v-else class="w-max-488 font-24 text-3">{{ orderDetailInfo.invoice_name }}</view>
        </view>

        <view class="ml-24 mr-24 pt-32 pb-32">
          <view class="font-28 font-wei text-3">发票须知</view>
          <view class="bg-f6f6f6 b-rad-10 mt-20 p-24">
            <view class="font-24 text-6 l-h-34">①开票金额为用户实际支付金额</view>
            <view class="font-24 text-6 l-h-34">②电子发票会在确认收货后发送到您的邮箱中</view>
          </view>
        </view>
      </view>

      <!-- 金额明细 -->
      <view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">商品金额</text>
          <text class="font-28 font-wei text-3">¥{{ orderDetailInfo.goods_price }}</text>
        </view>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">运费</text>
          <text class="font-24 font-wei text-3">+¥{{ orderDetailInfo.express_fee }}</text>
        </view>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">平台优惠</text>
          <text class="font-24 font-wei text-e80404">-¥{{ orderDetailInfo.preferential_reduction }}</text>
        </view>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">满减</text>
          <text class="font-24 font-wei text-e80404">-¥{{ orderDetailInfo.money_off_value }}</text>
        </view>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <view class="d-flex a-center">
            <text class="font-28 font-wei text-3">优惠券</text>
            <!-- <text class="b-s-01-ff6f6f b-rad-06 ml-08 ptb-02-plr-08 font-20 text-e80404">已选推荐优惠</text> -->
          </view>

          <view class="font-24 font-wei text-e80404">-¥{{ orderDetailInfo.coupon_value }}</view>
        </view>

        <view class="d-flex j-end a-center ml-24 mr-24 pt-32 pb-32">
          <text class="font-28 text-3">共{{ orderDetailInfo.total_qty }}件</text>
          <text class="ml-10 font-28 font-wei text-3">实付款：</text>
          <text class="font-32 font-wei text-e80404">¥{{ orderDetailInfo.payment_amount }}</text>
        </view>
      </view>

      <!-- 分割线 -->
      <vh-split-line
        :padding-top="52"
        :padding-bottom="32"
        :margin-left="10"
        :margin-right="10"
        text="猜你喜欢"
        :font-bold="true"
        :font-size="36"
        text-color="#333333"
        :show-image="true"
        image-src="https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png"
      />

      <!-- 猜你喜欢列表 -->
      <vh-goods-recommend-list />

      <view
        v-if="orderDetailInfo.is_support_change_ts || orderDetailInfo.is_support_upgrade_cold"
        class="p-fixed bottom-0 z-999 flex-e-c ptb-00-plr-24 w-p100 h-104 bg-ffffff b-sh-00021200-022"
      >
        <u-button
          v-if="orderDetailInfo.is_support_change_ts"
          shape="circle"
          :hair-line="false"
          :ripple="true"
          ripple-bg-color="#FFF"
          :custom-style="{
            margin: 0,
            width: '208rpx',
            height: '64rpx',
            fontSize: '28rpx',
            color: '#666',
            border: '1rpx solid #666',
          }"
          @click="showTsaPopup"
          >立即暂存</u-button
        >
        <u-button
          v-if="orderDetailInfo.is_support_upgrade_cold"
          shape="circle"
          :hair-line="false"
          :ripple="true"
          ripple-bg-color="#FFF"
          :custom-style="{
            margin: '0 0 0 20rpx',
            padding: '0 40rpx',
            width: 'auto',
            height: '64rpx',
            fontSize: '28rpx',
            color: '#fff',
            background: '#E80404',
            border: 'none',
          }"
          @click="ccdnPopupVisible = true"
          >升级冷链配送（¥{{ orderDetailInfo.upgrade_cold_fee }}）</u-button
        >
      </view>
      <!-- 底部按钮 -->
      <view
        v-else-if="orderDetailInfo.status != 1 && orderDetailInfo.status != 3 && orderDetailInfo.status != 7"
        class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022"
      >
        <!-- 待支付 -->
        <view v-if="orderDetailInfo.status == 0" class="h-104 d-flex j-end a-center pr-24">
          <view v-if="orderDetailInfo.countdown == 0" class="">
            <u-button
              shape="circle"
              :hair-line="false"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '208rpx',
                height: '64rpx',
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#999',
                border: '1rpx solid #999',
              }"
              @click="deleteOrder()"
              >删除订单</u-button
            >
          </view>
          <view v-else class="d-flex a-center">
            <!-- showCanOrdPop = true -->
            <u-button
              shape="circle"
              :hair-line="false"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '208rpx',
                height: '64rpx',
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#999',
                border: '1rpx solid #999',
              }"
              @click="showCanOrdPop = true"
              >取消订单</u-button
            >
            <u-button
              :disabled="orderDetailInfo.countdown == 0"
              shape="circle"
              :hair-line="false"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '208rpx',
                height: '64rpx',
                marginLeft: '20rpx',
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#FFF',
                backgroundColor: orderDetailInfo.countdown == 0 ? '#FCE4E3' : '#E80404',
                border: 'none',
              }"
              @click="immediatePayment()"
              >立即支付</u-button
            >
          </view>
        </view>

        <!-- 已发货（待收货） -->
        <view v-if="orderDetailInfo.status == 2" class="h-104 d-flex j-center a-center">
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '646rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#FFF',
              backgroundColor: '#E80404',
              border: 'none',
            }"
            @click="confirmReceipt"
            >确认收货</u-button
          >
        </view>

        <!-- 交易关闭 -->
        <view v-if="orderDetailInfo.status == 4" class="h-104 d-flex j-end a-center pr-24">
          <view class="">
            <u-button
              shape="circle"
              :hair-line="false"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '208rpx',
                height: '64rpx',
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#999',
                border: '1rpx solid #999',
              }"
              @click="deleteOrder()"
              >删除订单</u-button
            >
          </view>
        </view>
        <view v-if="orderDetailInfo.status == 5" class="h-104 d-flex j-end a-center pr-24">
          <view class="">
            <u-button
              shape="circle"
              :hair-line="false"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '208rpx',
                height: '64rpx',
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#999',
                border: '1rpx solid #999',
              }"
              @click="showCanOrdPop = true"
              >取消订单</u-button
            >
          </view>
        </view>

        <!-- 已暂存 -->
        <view v-if="orderDetailInfo.status == 6" class="flex-e-c pr-24 h-p100">
          <view v-if="orderDetailInfo.ts_time" class="mr-10 font-wei-450 text-f3a549" @click="openTsSelect">
            暂存至：{{ orderDetailInfo.ts_time }}<u-icon class="ml-04" name="edit-pen"></u-icon>
          </view>
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              margin: 0,
              width: '208rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#FFF',
              backgroundColor: '#E80404',
              border: 'none',
            }"
            @click="shipNow"
            >立即发货</u-button
          >
        </view>

        <!-- 暂未使用 -->
        <view v-if="false" class="h-104 d-flex j-end a-center pr-24">
          <u-button
            shape="circle"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '208rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#999',
              border: '2rpx solid #999',
            }"
            >删除订单</u-button
          >
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '208rpx',
              height: '64rpx',
              marginLeft: '20rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#FFF',
              backgroundColor: '#E80404',
              border: 'none',
            }"
            >评价晒单</u-button
          >
        </view>
      </view>

      <!-- 悬浮按钮 -->
      <view v-if="orderDetailInfo.is_raffle && isShowHoverButton" class="fade-in p-fixed z-999 bottom-144 right-24">
        <view class="p-rela w-186 h-200">
          <image
            class="p-abso z-04 top-0 right-0 w-26 h-26 p-20"
            :src="ossIcon(`/order_detail/h_del.png`)"
            mode="aspectFill"
            @click="isShowHoverButton = false"
          />
          <view
            class="p-abso bottom-0 left-0"
            @click="jump.appAndMiniJump(1, `${routeTable.pBPaySuccess}?plate=orderDetail`, $vhFrom)"
          >
            <vh-image :src="ossIcon(`/order_detail/h_draw.png`)" :width="160" :height="160" />
          </view>
        </view>
      </view>

      <!-- 取消订单弹框 -->
      <u-modal
        v-model="showCanOrdPop"
        :width="500"
        :show-title="false"
        content="是否取消该订单"
        :show-cancel-button="true"
        cancel-text="否"
        cancel-color="#2E7BFF"
        confirm-text="是"
        confirm-color="#2E7BFF"
        @confirm="cancelOrder"
      />
    </view>

    <!-- 骨架屏 -->
    <view v-else class="fade-in">
      <u-navbar
        back-icon-color="#FFF"
        title="订单详情"
        title-size="36"
        :title-bold="true"
        title-color="#FFF"
        :background="{ background: '#E80404' }"
      />
      <vh-skeleton :type="3" loading-color="#E80404" />
    </view>

    <OrderAddressUpdateConfirmPopup v-model="oaucPopupVisible" :orderDetail="orderDetailInfo" />
    <TsServiceAgreementPopup v-model="tsaPopupVisible" @agree="onTsNow" />
    <ColdChainDeliverNoticePopup
      v-model="ccdnPopupVisible"
      :isCross="orderDetailInfo.order_type === 2 ? 1 : 0"
      @agree="onUpgradeColdChain"
    />

    <!-- 使用现有的暂存服务协议弹窗组件 -->

    <u-select
      :mask-close-able="false"
      v-model="tsConfirm"
      mode="mutil-column-auto"
      :list="tsTimeList"
      :default-value="defaultTsTimeIndex"
      @cancel="cancelTsTime"
      @confirm="confirmTsTime"
      title="请选择暂存时间"
    ></u-select>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'order-detail',
  data() {
    return {
      loading: true,
      navBackgroundColor: 'rgba(224, 20, 31, 0)',
      orderNo: '',
      orderDetailInfo: {},
      shareInfo: {},
      logisticInfo: {},
      showCanOrdPop: false,
      isShowHoverButton: true,
      oaucPopupVisible: false,
      tsaPopupVisible: false,
      ccdnPopupVisible: false,
      coldChainUpgradeStatus: false,
      shareShortUrl: '',
      currentDate: '',
      datePickerParams: {
        minDate: '',
        maxDate: '',
      },
      tsConfirm: false,
      tsTimeList: [],
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable', 'logisticsInfo']),

    // 获取拼团状态
    getSpellStatus() {
      if (this.orderDetailInfo.status !== 5) return 0
      if (this.orderDetailInfo?.groupInfo?.group_status && this.orderDetailInfo.groupInfo.group_status != 2) {
        return this.orderDetailInfo.groupInfo.group_status
      }
      return false
    },

    // 暂存遮罩层样式
    TempMaskStyle() {
      let style = {}
      style.backgroundColor = 'rgba(0, 0, 0, 0.6)'
      if (this.tsaPopupVisible) style.zIndex = this.$u.zIndex.mask
      else style.zIndex = -1
      style.transition = `all 0.3s ease-in-out`
      return style
    },

    // 计算默认选中的时间索引
    defaultTsTimeIndex() {
      if (!this.tsTimeList.length) return []

      // 获取最后一年的索引
      const lastYearIndex = this.tsTimeList.length - 1
      const lastYear = this.tsTimeList[lastYearIndex]

      // 获取最后一个月的索引
      const lastMonthIndex = lastYear.children.length - 1
      const lastMonth = lastYear.children[lastMonthIndex]

      // 获取最后一天的索引
      const lastDayIndex = lastMonth.children.length - 1

      return [lastYearIndex, lastMonthIndex, lastDayIndex]
    },
  },

  onLoad(options) {
    this.getShareInfo()
    this.orderNo = options.orderNo
  },
  onShow() {
    if (this.coldChainUpgradeStatus) {
      this.feedback.showWithoutCancelModal({
        title: '',
        content: '冷链配送服务开启成功,到达配送站点是常温配送,请兔友们注意及时取件和收货,感谢您的支持。',
      })
      this.coldChainUpgradeStatus = false
      this.delayGetOrderDetail()
    } else {
      this.init()
    }
  },

  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muLogisticsInfo', 'muPayInfo']),
    async getShareInfo() {
      const res = await this.$u.api.getShareInfo()
      console.log(res.data)
      this.shareInfo = res.data
    },
    // 初始化
    async init() {
      await this.getOrderDetail()
      await this.getLogisticsDetail()
      this.loading = false
    },

    // 获取订单详情
    async getOrderDetail() {
      let res = await this.$u.api.orderDetail({ order_no: this.orderNo })
      this.orderDetailInfo = res.data
      // 更新日期选择器的时间范围
      this.datePickerParams.minDate = this.orderDetailInfo.begin_storage_time
      this.datePickerParams.maxDate = this.orderDetailInfo.latest_storage_time
      if (this.$vhFrom == 'next' && this.orderDetailInfo.groupInfo) {
        this.getshortUrl()
      }
    },

    async getshortUrl() {
      let shareLongStr = `https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/goods-detail.html?id=${this.orderDetailInfo.groupInfo.period}&groupId=${this.orderDetailInfo.groupInfo.group_id}`
      // let https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/goods-detail.html?id=155130
      let res = await this.$u.api.urlShorten({ url: shareLongStr })
      this.shareShortUrl = res.data
      // this.orderDetailInfo.express_number = 'SF1346169995138'
    },
    goodsClick(item) {
      console.log('goodsClick', this.$vhFrom)

      this.jump.appAndMiniJump(1, `${this.$routeTable.pgGoodsDetail}?id=${item.period}`, this.$vhFrom)
    },
    // 查询物流轨迹
    async getLogisticsDetail() {
      const { order_no, express_type, express_number, status } = this.orderDetailInfo
      if (status === 1 || status === 2) {
        let res = await this.$u.api.logisticsDetails({
          logisticCode: express_number,
          sub_order_no: order_no,
          expressType: express_type,
          order_type: this.orderDetailInfo.order_type,
        })
        this.logisticInfo = res.data
      }
    },

    // 查看物流
    viewLogistics() {
      let { goodsInfo, express_type, express_number } = this.orderDetailInfo
      this.muLogisticsInfo({ image: goodsInfo[0].goods_img, expressType: express_type, logisticCode: express_number })
      this.jump.navigateTo(
        `/packageB/pages/logistics-detail/logistics-detail?info=${JSON.stringify(this.logisticInfo)}`
      )
      // this.jump.appAndMiniJump(1, `/packageB/pages/logistics-detail/logistics-detail?info=${JSON.stringify(this.logisticInfo)}`, this.$vhFrom)
    },

    // 立即支付
    async immediatePayment() {
      const is_cross = this.orderDetailInfo.order_type == 2 ? 1 : 0
      this.muPayInfo({ ...this.orderDetailInfo, is_cross })
      if (this.$vhFrom == 'next') {
        // uni.setStorageSync('nextpayInfo',payInfo);
        if (is_cross == 1) {
          uni.setStorageSync('nextpayInfo', { ...this.orderDetailInfo, is_cross })
          this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
        } else {
          this.jump.pullAppPay(this.$vhFrom, { main_order_no: this.orderDetailInfo.order_no })
        }
      } else {
        this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
      }

      // if(this.$vhFrom == 'next'){
      //     uni.setStorageSync('nextpayInfo',{ ...this.orderDetailInfo, is_cross });
      //   }
      // this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
      // this.jump.redirectTo(this.routeTable.pBPayment)
    },

    // 删除订单
    deleteOrder() {
      this.feedback.showModal({
        content: `确认删除该订单吗？`,
        confirm: async () => {
          try {
            await this.$u.api.cancelDeleteOrder({ type: 2, order_no: this.orderDetailInfo.order_no })
            this.feedback.toast({ title: '删除成功', icon: 'success' })
            setTimeout(() => {
              this.jump.jumpPrePage(this.$vhFrom)
            }, 1500)
          } catch (e) {}
        },
      })
    },

    // 取消订单
    async cancelOrder() {
      this.feedback.loading({ title: '取消订单中' })
      if (this.orderDetailInfo.status == 5) {
        const data = {
          sub_order_no: this.orderDetailInfo.order_no,
          order_type: this.orderDetailInfo.order_type,
          type: 1,
          replenish_imgs: '',
          replenish_video: '',
          refund_reason: '不想要/多拍/错拍',
          replenish_content: '拼团未成功,用户取消订单',
          goods_status: 1,
          ts_time: 0,
          ts_money: '0.00',
        }
        try {
          await this.$u.api.applyAfterSale(data)
          this.feedback.toast({ title: '取消成功', icon: 'success' })
          setTimeout(() => {
            this.jump.jumpPrePage(this.$vhFrom)
          }, 1500)
        } catch (e) {}
      } else {
        try {
          await this.$u.api.cancelDeleteOrder({ type: 1, order_no: this.orderDetailInfo.order_no })
          this.feedback.toast({ title: '取消成功', icon: 'success' })
          setTimeout(() => {
            this.jump.jumpPrePage(this.$vhFrom)
          }, 1500)
        } catch (e) {}
      }
    },

    // 确认收货
    async confirmReceipt() {
      this.feedback.showModal({
        content: `您确认收到货了吗？`,
        confirm: async () => {
          try {
            let data = {}
            data.sub_order_no_str = this.orderDetailInfo.order_no
            let res = await this.$u.api.orderReceipt(data)
            this.feedback.toast({ title: '确认成功', icon: 'success' })
            this.orderDetailInfo.status = 3
          } catch (e) {}
        },
      })
    },
    inviteGroup() {
      if (this.$vhFrom == 'next') {
        this.copy.copyText(this.shareShortUrl)
        this.feedback.toast({ title: '链接已复制，请分享链接邀请好友参团' })
      } else {
        this.feedback.toast({ title: '请前往APP或小程序查看此功能~' })
      }
    },
    // 立即发货
    shipNow() {
      this.feedback.showModal({
        content: `确认立即发货吗？`,
        confirm: async () => {
          try {
            let data = {}
            data.order_no = this.orderDetailInfo.order_no
            data.order_type = this.orderDetailInfo.order_type
            data.is_ts = 0
            let res = await this.$u.api.updateOrderStatus(data)
            this.feedback.toast({ title: '确认成功~', icon: 'success' })
            this.delayGetOrderDetail()
          } catch (e) {}
        },
      })
    },
    delayGetOrderDetail() {
      const timer = setTimeout(() => {
        this.getOrderDetail()
        timer && clearTimeout(timer)
      }, 1.2 * 1000)
    },
    onTsOrder() {
      const { order_no, order_type } = this.orderDetailInfo
      const params = {
        sub_order_no: order_no,
        order_type,
      }
      this.$u.api.updateOrderTsStatus(params).then(() => {
        this.feedback.toast({ title: '暂存成功~', icon: 'success' })
        this.delayGetOrderDetail()
      })
    },
    onUpgradeColdChain() {
      const { order_no, order_type, upgrade_cold_fee } = this.orderDetailInfo
      const params = {
        sub_order_no: order_no,
        order_type,
      }
      this.$u.api.upgradeColdChain(params).then((res) => {
        const { main_order_no, countdown } = res.data
        this.muPayInfo({
          payPlate: 7,
          main_order_no,
          payment_amount: upgrade_cold_fee,
          countdown,
          is_cross: 0,
        })
        // this.jump.navigateTo(this.routeTable.pBPayment)
        this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom)
      })
    },

    onJumpInvoiceManagement() {
      const { order_no, order_type } = this.orderDetailInfo
      // this.jump.navigateTo(
      //   `${this.$routeTable.pEInvoiceMangement}?comeFrom=3&orderNo=${order_no}&orderType=${order_type}`
      // )
      this.jump.appAndMiniJump(
        1,
        `${this.$routeTable.pEInvoiceMangement}?comeFrom=3&orderNo=${order_no}&orderType=${order_type}`,
        this.$vhFrom
      )
    },

    // 修改存时间
    changeTsTime() {
      this.showDatePicker = true
    },

    // 确认选择日期
    async confirmDate(selectedDate) {
      try {
        const { order_no, order_type } = this.orderDetailInfo
        await this.$u.api.updateOrderTsTime({
          sub_order_no: order_no,
          ts_time: selectedDate,
        })

        this.feedback.toast({ title: '修改时间成功', icon: 'success' })
        this.orderDetailInfo.ts_time = selectedDate
        this.currentDate = selectedDate
        this.tsConfirm = false
      } catch (error) {
        this.feedback.toast({ title: '修改失败，请重试', icon: 'none' })
      }
    },

    // 取消选择日期
    cancelDate() {
      console.log('取消选择日期')
    },

    // // 链接跳转
    // linkpressJump(e) {
    //   e.ignore()
    //   const trimHref = this.$u.trim(e.href, 'all')
    //   this.jump.jumpH5Agreement(trimHref)
    // },

    // 显示暂存协议
    async showTsaPopup() {
      try {
        if (!this.hasGotTempText) {
          this.feedback.loading()
          let res = await this.$u.api.orderRelaText({ type: 3 })
          this.tempStorText = res.data[0].content
          this.hasGotTempText = true
        }
        this.tsaPopupVisible = true

        uni
          .createSelectorQuery()
          .in(this)
          .select('#temp-text-con')
          .boundingClientRect((res2) => {
            uni.upx2px(788) < res2.height
              ? (this.isReadTempStorageComplete = false)
              : (this.isReadTempStorageComplete = true)
          })
          .exec()
      } catch (e) {
        this.feedback.toast({ title: '出了点小意外~' })
      }
    },

    // // 点击立即暂存按钮
    // showTsaPopup() {
    //   this.tsaPopupVisible = true
    // },

    // 同意暂存协议
    async onTsNow() {
      try {
        if (
          this.orderDetailInfo.begin_storage_time &&
          this.orderDetailInfo.latest_storage_time &&
          this.orderDetailInfo.tstime_is_enable
        ) {
          console.log(this.orderDetailInfo)
          const minDate = new Date(this.orderDetailInfo.begin_storage_time).getTime()
          const maxDate = new Date(this.orderDetailInfo.latest_storage_time).getTime()
          this.tsTimeList = this.generateTsTimeList(minDate, maxDate)
          this.tsConfirm = true
        } else {
          this.onTsOrder()
        }
      } catch (e) {
        this.feedback.toast({ title: '获取暂存时间失败' })
      }
    },

    // 取消选择暂存时间
    cancelTsTime() {
      this.tsConfirm = false
    },

    // 确认暂存时间
    async confirmTsTime(e) {
      if (!e || !e.length) return

      const [year, month, day] = e.map((item) => item.value)
      const selectedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

      // 验证选择时间是否在允许范围内
      const selectedTime = new Date(selectedDate).getTime()
      const minDate = new Date(this.datePickerParams.minDate).getTime()
      const maxDate = new Date(this.datePickerParams.maxDate).getTime()

      if (selectedTime < minDate - 86400000) {
        // 减去一天的毫秒数，允许选择最早时间
        this.feedback.toast({
          title: '暂存发货时间需大于等于最早可选时间',
          duration: 2000,
        })
        return
      }

      if (selectedTime > maxDate) {
        this.feedback.toast({
          title: '暂存发货时间不能超过限定时间',
          duration: 2000,
        })
        return
      }

      if (this.orderDetailInfo.status === 1) {
        this.setTsOrder(selectedDate)
      } else if (this.orderDetailInfo.status === 6) {
        this.confirmDate(selectedDate)
      }
    },
    async setTsOrder(selectedDate) {
      try {
        await this.$u.api.updateOrderTsStatus({
          sub_order_no: this.orderDetailInfo.order_no,
          ts_time: selectedDate,
          order_type: this.orderDetailInfo.order_type,
        })
        this.feedback.toast({
          title: '暂存成功',
          icon: 'success',
          duration: 1500,
        })
        this.tsConfirm = false
        setTimeout(() => {
          this.getOrderDetail()
        }, 1500)
      } catch (e) {
        this.feedback.toast({ title: '暂存失败' })
      }
    },
    // 生成可选时间列表
    generateTsTimeList(minDate, maxDate) {
      const start = new Date(minDate)
      const end = new Date(maxDate)
      const result = []

      // 生成年份
      for (let y = start.getFullYear(); y <= end.getFullYear(); y++) {
        const yearItem = {
          value: y,
          label: `${y}年`,
          children: [],
        }

        // 生成月份
        const startMonth = y === start.getFullYear() ? start.getMonth() + 1 : 1
        const endMonth = y === end.getFullYear() ? end.getMonth() + 1 : 12

        for (let m = startMonth; m <= endMonth; m++) {
          const monthItem = {
            value: m,
            label: `${m}月`,
            children: [],
          }

          // 生成日期
          const daysInMonth = new Date(y, m, 0).getDate()
          const startDay = y === start.getFullYear() && m === start.getMonth() + 1 ? start.getDate() : 1
          const endDay = y === end.getFullYear() && m === end.getMonth() + 1 ? end.getDate() : daysInMonth

          for (let d = startDay; d <= endDay; d++) {
            monthItem.children.push({
              value: d,
              label: `${d}日`,
            })
          }

          // 只添加有效日期的月份
          if (monthItem.children.length > 0) {
            yearItem.children.push(monthItem)
          }
        }

        // 只添加有效月份的年份
        if (yearItem.children.length > 0) {
          result.push(yearItem)
        }
      }

      return result
    },

    // 打开时间选择
    openTsSelect() {
      if (this.orderDetailInfo.tstime_is_enable) {
        const minDate = new Date(this.datePickerParams.minDate).getTime()
        const maxDate = new Date(this.datePickerParams.maxDate).getTime()
        this.tsTimeList = this.generateTsTimeList(minDate, maxDate)
        this.tsConfirm = true
      } else {
        this.feedback.toast({
          title: this.orderDetailInfo.ts_err_msg,

          duration: 2000,
        })
      }
    },
  },

  onShareAppMessage(res) {
    console.log(res)
    if (this.getSpellStatus == 1) {
      const { goodsInfo, order_type, groupInfo } = this.orderDetailInfo //拿到拼团订单信息
      let url = `/pages/goods-detail/goods-detail?id=${goodsInfo[0].period}&channel=${order_type}&groupId=${groupInfo.group_id}`
      console.log(url)
      return {
        //按钮分享
        title: this.shareInfo.main_title,
        path: `/pages/goods-detail/goods-detail?id=${goodsInfo[0].period}&channel=${order_type}&groupId=${groupInfo.group_id}`,
        imageUrl: this.shareInfo.share_image,
      }
    } else {
      return {
        title: '酒云网 与百万发烧友一起淘酒',
        path: '/pages/index/index',
      }
    }
  },

  onPageScroll(res) {
    res.scrollTop <= 100
      ? (this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop / 100})`)
      : (this.navBackgroundColor = `rgba(224, 20, 31, 1)`)
  },
}
</script>

<style scoped></style>
