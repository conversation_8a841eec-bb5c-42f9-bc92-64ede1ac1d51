<template>
	<view v-if="payInfo.main_order_no || payInfo.order_no" class="content h-vh-100 bg-f5f5f5 o-hid">
		<!-- 导航栏 -->
		<u-navbar :is-back="false" title="收银台" title-size="36" title-color="#fff" :background="{ background: '#C8101B'}">
			<image class="ml-24 w-44 h-44" src="https://images.vinehoo.com/vinehoomini/v3/payment/back_arr.png" mode="aspectFill" @click="showLeaveModel = true" />
		</u-navbar>

		<!-- 支付金额 + 支付剩余时间 -->
		<view class="pt-50 h-376 bg-c8101b">
			<view class="d-flex j-center a-center">
				<text class="font-36 font-wei text-ffffff">¥</text>
				<text class="ml-08 font-72 font-wei text-ffffff">{{displayPaymentAmount}}</text>
			</view>
			<view v-if="payInfo.payPlate != 1" class="d-flex j-center mt-08">
				<view class="font-28 text-ffffff">剩余有效时间：</view>
				<vh-count-down ref="uCountDown" :show-days="false" :timestamp="payInfo.countdown"
				bg-color="transparent" font-size="28" separator-size="24" separator-color="#fff" color="#fff"
				@end="countDownEnd()" />
			</view>
		</view>

		<!-- 支付方式 -->
		<view class="mt-n-72 b-rad-10 b-sh-00041200-013 w-702 bg-ffffff mtb-00-mlr-auto">
			<view v-for="(item, index) in threePayTypeList" :key="index">
				<view v-if="index" class="w-638 h-02 bg-eeeeee mtb-00-mlr-auto"></view>
				<view class="flex-sb-c ptb-00-plr-32 h-160" @click="handlePayTypeSelect(item)" :class="{'disabled': isPayTypeDisabled(item)}">
					<view class="flex-c-c">
						<image :src="ossIcon(item.icon)" class="w-48 h-48" />
						<text class="ml-12 font-32 text-3 l-h-44">{{ item.name }}</text>
					</view>
					<image :src="ossIcon(`${isPayTypeSelected(item) ? '/payment/cir_sel_h_36.png' : '/payment/cir_sel_40.png'}`)" :class="isPayTypeSelected(item) ? 'w-36 h-36' : 'w-40 h-40'" />
				</view>
			</view>
		</view>

		<!-- 支付按钮 -->
		<view class="flex-c-c mt-136">
			<u-button shape="circle" :loading="paying" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
			:custom-style="{width:'678rpx', height:'80rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="handlePay">{{ paying ? '支付中...' : '支付' }}</u-button>
		</view>

		<view v-html="ailPayForm"></view>

		<!-- 离开弹窗 -->
		<u-modal
			v-model="showLeaveModel"
			:width="600"
			title="确认要放弃付款吗"
			:title-style="{fontSize:'28rpx', fontWeight:'bold', color:'#333'}"
			:show-cancel-button="true"
			cancel-text="残忍离开"
			confirm-text="继续支付"
			:cancel-style="{fontSize:'28rpx', color:'#999'}"
			:confirm-style="{fontSize:'28rpx', color:'#E80404'}"
			content="好货不等人,请尽快支付~"
			:content-style="{fontSize:'28rpx', color:'#333'}"
			@cancel="leave()"
			@confirm="showLeaveModel = false"/>

		<u-popup v-model="toPublicPopupVisible" mode="center" width="434rpx" height="520rpx" border-radius="20" class="bg-transparent-popup">
			<view class="p-rela w-p100 h-p100">
				<image class="p-abso w-p100 h-p100" :src="ossIcon(`/payment/co_bg.png`)" />
				<view class="p-rela">
					<view class="pt-32 font-wei-500 font-28 text-3 l-h-40 text-center">
						<view>如需对公付款</view>
						<view>请扫码添加专属客服</view>
					</view>
					<view class="flex-c-c mt-78">
						<vh-image :src="toPublicKefuQrCode" :width="282" :height="282" :loadingType="2" :showMenuByLongpress="true" />
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	import { MAppPaymentSource, MPaymentMethod } from '@/common/js/utils/mapperModel'
	import { WX_APPID_PROD } from '@/common/js/fun/constant'
	import wx from 'weixin-js-sdk'

	const THREE_PAY_TYPE = {
		WX: 1,
		ALI: 2,
		ToPublic: 3,
		BALANCE: 4,
	}

	const TPT_WX_CONFIG = { type: THREE_PAY_TYPE.WX, name: '微信支付', icon: '/payment/wx_48.png' }
	const TPT_ALI_CONFIG = { type: THREE_PAY_TYPE.ALI, name: '支付宝支付', icon: '/payment/ali_48.png' }
	const TPT_BALANCE_CONFIG = { type: THREE_PAY_TYPE.BALANCE, name: '储值账户支付', icon: '/payment/balance_48.png' }
	const TPT_ToPublic_CONFIG = { type: THREE_PAY_TYPE.ToPublic, name: '对公付款', icon: '/payment/to_public_48.png' }

	export default{
		name:'payment',

		data: () => ({
			showLeaveModel: false, //是否显示离开弹框
			paying: false, //是否处于支付中
			enterCount: 0, //进入次数
			threePayType: THREE_PAY_TYPE.WX,
			threePayTypeList: [TPT_WX_CONFIG, TPT_ALI_CONFIG, TPT_ToPublic_CONFIG],
			payParams: {},
			h5WxPayOrigin: 'https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/vinehoo-pay.html',
			payment_method: 1,
			isWxProcess: false,
			ailPayForm: '',
			code: '',
			wxPayOpenId: '',
			orderNo: '',
			payPlate: '',
			toPublicPopupVisible: false,
			toPublicKefuQrCode: '',
			// 余额支付相关
			balanceInfo: {
				recharge_balance: 0,
				bonus_balance: 0
			},
			useBalanceAmount: 0, // 使用的余额金额
			canUseOtherPayment: false, // 是否可以使用其他支付方式
			selectedPayTypes: [], // 已选择的支付方式
			originalPaymentAmount: 0, // 原始支付金额
			userHasInteracted: false, // 用户是否已经交互过支付方式
		}),

		computed:{
			//Vuex 辅助state函数
			...mapState(['payInfo', 'requestPrefix', 'routeTable']),
			hasPayInfo({ payInfo, orderNo }) {
				const { main_order_no, order_no } = payInfo
				return !!main_order_no || !!order_no || !!orderNo
			},
			isAppPay ({ payInfo }) {
				return payInfo.$isAppPay
			},
			isIosSmzdmapp () {
				const ua = window.navigator.userAgent.toLowerCase()
				return /iphone_smzdmapp/.test(ua)
			},
			// 计算总余额
			totalBalance() {
				return this.balanceInfo.recharge_balance + this.balanceInfo.bonus_balance
			},
			// 余额支付选项名称
			balancePaymentName() {
				return `储值账户支付（剩余：¥${this.totalBalance.toFixed(2)}）`
			},

			// 计算实际显示的支付金额
			displayPaymentAmount() {
				
				return this.payInfo.cash_amount ? this.payInfo.cash_amount : this.payInfo.payment_amount;
			}
		},

		onLoad (options) {
			this.isWxProcess = this.juageWxProcess()
			this.orderNo = options.orderNo
			this.payPlate = +options.payPlate
			if(this.$vhFrom == 'next'){
				const nextpayInfo = uni.getStorageSync('nextpayInfo');
			 	this.muPayInfo(nextpayInfo)
			 	uni.removeStorageSync('nextpayInfo');
        	}

			console.log(this.payInfo)
			if (!this.hasPayInfo) return

			// 保存原始支付金额
			this.originalPaymentAmount = parseFloat(this.payInfo.payment_amount || 0)

			// 获取用户余额信息
			this.getBalanceInfo()

			if (this.payInfo.is_cross) {
				const payPlate = 5
				this.muPayInfo({
					...this.payInfo,
					payPlate,
					$isAppPay: true,
					$paySuccessReturnUrl: this.routeTable.pBPaySuccess,
				})
				this.payPlate = payPlate
			}
			if (this.isWxProcess) {
				console.log('988333rfff---');
				this.threePayTypeList = [TPT_WX_CONFIG, TPT_ToPublic_CONFIG]
				if ([4, 5].includes(this.payPlate)) {
					this.threePayTypeList = [TPT_WX_CONFIG]
					if ([5].includes(this.payPlate)) {
						this.threePayTypeList = [TPT_WX_CONFIG, TPT_ToPublic_CONFIG]
					}
					if ([5].includes(this.payPlate) && !this.orderNo) {
						const { main_order_no, order_no } = this.payInfo
						window?.history?.replaceState(null, '', `?orderNo=${main_order_no || order_no}&payPlate=${this.payPlate}`)
					}
					const { code } = options
					if (this.$isDev) {
						if (!code) {
							location.href = `https://activity.vinehoo.com/activities-v3/RedirectFetchWxCode?appid=${WX_APPID_PROD}&redirectUrl=${encodeURIComponent(window.location.href.split("#")[0])}`
							return
						} else {
							this.code = code
						}
					} else {
						if (!code) {
							location.href =
								'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
								WX_APPID_PROD +
								'&redirect_uri=' +
								encodeURIComponent(window.location.href) +
								`&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect`
							return
						} else {
							this.code = code
						}
					}
					this.loadWxPayOpenId()
					this.wxConfig()
				}
				if (this.orderNo) this.loadOrderInfo()
			} else {
		console.log('3444444444---');

				if ([4].includes(this.payPlate)) {
					this.threePayTypeList = [TPT_ALI_CONFIG]
				} else if ([5].includes(this.payPlate)) {
					this.threePayTypeList = [TPT_ALI_CONFIG, TPT_ToPublic_CONFIG]
				} else if ([60].includes(this.payPlate)) {
					this.threePayTypeList = [TPT_ALI_CONFIG]
				}
				if (this.orderNo) this.loadOrderInfo()
			}
			// 重新排序支付方式，将储值账户支付放在支付宝和对公付款之间
			this.updatePaymentTypeList()

			// 默认选中微信支付
			this.threePayType = THREE_PAY_TYPE.WX

			// 初始化已选择的支付方式为微信支付
			this.selectedPayTypes = [THREE_PAY_TYPE.WX]
		},

		onShow() {
			if (!this.hasPayInfo) {
				const { payPlate } = this.payInfo
				if( payPlate === 6 ) {
					this.jump.appAndMiniJump(0,this.$routeTable.pBOrderDeposit, this.$vhFrom, 1)
					// this.jump.reLaunch(this.$routeTable.pBOrderDeposit)
				}else {
					// this.jump.reLaunch('/packageE/pages/my-order/my-order?status=1')
					this.jump.appAndMiniJump(0,'/packageE/pages/my-order/my-order?status=1', this.$vhFrom, 1)
				}
				return
			}
			this.enterCount++
			if( this.enterCount >= 2 ) {
				this.queryOrderStatus()
			}
		},

		methods:{
			...mapMutations(['muPayInfo']),

			juageWxProcess() {
				const ua = window.navigator.userAgent.toLowerCase()
				return /micromessenger/.test(ua)
			},

			// 获取余额信息
			async getBalanceInfo() {
				try {
					let res = await this.$u.api.myCurrentBalance()
					const data = res.data;
					this.balanceInfo = data;

					// 计算可用余额
					const paymentAmount = parseFloat(this.payInfo.payment_amount);

					// 如果余额为0，则不可选择余额支付，但仍然显示该选项
					if (this.totalBalance <= 0) {
						// 确保默认选中微信支付
						if (!this.selectedPayTypes.includes(THREE_PAY_TYPE.WX)) {
							this.selectedPayTypes = [THREE_PAY_TYPE.WX];
							this.threePayType = THREE_PAY_TYPE.WX;
						}

						// 更新储值账户支付选项名称，显示余额为0
						const balanceConfig = this.threePayTypeList.find(item => item.type === THREE_PAY_TYPE.BALANCE);
						if (balanceConfig) {
							balanceConfig.name = `储值账户支付（剩余：¥0.00）`;
						}
						return;
					}

					// 如果余额足够支付全部金额
					if (this.totalBalance >= paymentAmount) {
						this.useBalanceAmount = paymentAmount;
						this.canUseOtherPayment = false;
					} else {
						// 余额不足，使用全部余额，并允许选择其他支付方式
						this.useBalanceAmount = this.totalBalance;
						this.canUseOtherPayment = true;
					}

					// 更新储值账户支付选项
					const balanceConfig = this.threePayTypeList.find(item => item.type === THREE_PAY_TYPE.BALANCE);
					if (balanceConfig) {
						balanceConfig.name = this.balancePaymentName;
					}
				} catch(e) {
					console.error('获取余额信息失败', e);
				}
			},

			// 更新支付方式列表
			updatePaymentTypeList() {
				// 创建一个新的支付方式列表
				let newPayTypeList = [];

				// 检查是否有微信支付选项
				const wxConfig = this.threePayTypeList.find(item => item.type === THREE_PAY_TYPE.WX);
				if (wxConfig) {
					newPayTypeList.push(wxConfig);
				} else {
					newPayTypeList.push(TPT_WX_CONFIG);
				}

				// 检查是否有支付宝支付选项
				const aliConfig = this.threePayTypeList.find(item => item.type === THREE_PAY_TYPE.ALI);
				if (aliConfig) {
					newPayTypeList.push(aliConfig);
				} else {
					newPayTypeList.push(TPT_ALI_CONFIG);
				}

				// 添加储值账户支付选项（在支付宝和对公付款之间）
				const balanceConfig = {
					...TPT_BALANCE_CONFIG,
					name: this.totalBalance > 0 ? this.balancePaymentName : `储值账户支付（剩余：¥0.00）`
				};
				newPayTypeList.push(balanceConfig);

				// 如果payInfo中的recharge_balance没有值，才添加对公付款选项
				if (!this.payInfo.recharge_balance) {
					// 检查是否有对公付款选项
					const toPublicConfig = this.threePayTypeList.find(item => item.type === THREE_PAY_TYPE.ToPublic);
					if (toPublicConfig) {
						newPayTypeList.push(toPublicConfig);
					} else {
						newPayTypeList.push(TPT_ToPublic_CONFIG);
					}
				}

				// 更新支付方式列表
				this.threePayTypeList = newPayTypeList;
				console.log('eeeeeeeeeeeeeeee-', newPayTypeList);

				// 初始化已选择的支付方式
				this.selectedPayTypes = [THREE_PAY_TYPE.WX];
				this.threePayType = THREE_PAY_TYPE.WX;
			},

			// 处理支付类型选择
			handlePayTypeSelect(item) {
				// 如果余额为0且尝试选择余额支付，则不允许选择
				if (item.type === THREE_PAY_TYPE.BALANCE && this.totalBalance <= 0) {
					this.feedback.toast({ title: '账户余额为0，无法使用余额支付' });
					return;
				}

				// 如果点击的是已选中的项目，且不是唯一选中的项目，则取消选择
				if (this.isPayTypeSelected(item) && this.selectedPayTypes.length > 1) {
					this.selectedPayTypes = this.selectedPayTypes.filter(type => type !== item.type);
					// 更新当前支付方式
					this.threePayType = this.selectedPayTypes[0];

					return;
				}

				// 如果点击的是唯一选中的项目，不允许取消选择
				if (this.isPayTypeSelected(item) && this.selectedPayTypes.length === 1) {
					return;
				}

				// 如果选择的是对公付款
				if (item.type === THREE_PAY_TYPE.ToPublic) {
					// 如果payInfo中的recharge_balance有值，就禁用对公支付
					if (this.payInfo.recharge_balance) {
						this.feedback.toast({ title: '当前订单不支持对公付款' });
						return;
					}

					// 清除已选择的余额支付
					this.selectedPayTypes = this.selectedPayTypes.filter(type => type !== THREE_PAY_TYPE.BALANCE);
					// 添加对公付款
					this.selectedPayTypes = [item.type];
					this.threePayType = item.type;

					return;
				}

				// 如果选择的是储值账户支付，且余额足够支付全部金额
				if (item.type === THREE_PAY_TYPE.BALANCE && !this.canUseOtherPayment) {
					this.selectedPayTypes = [item.type];
					this.threePayType = item.type;

					return;
				}

				// 如果选择的是储值账户支付，且余额不足
				if (item.type === THREE_PAY_TYPE.BALANCE && this.canUseOtherPayment) {
					// 如果已经选择了对公付款，则不能选择余额支付
					if (this.selectedPayTypes.includes(THREE_PAY_TYPE.ToPublic)) {
						this.feedback.toast({ title: '对公付款不能与余额支付同时选择' });
						return;
					}

					// 清除之前选择的其他支付方式（微信或支付宝），保留一个当前选择的
					const otherPayType = this.selectedPayTypes.find(type =>
						type !== THREE_PAY_TYPE.BALANCE && type !== THREE_PAY_TYPE.ToPublic
					);

					// 如果已经选择了其他支付方式，则保留该支付方式，否则只选择余额支付
					if (otherPayType) {
						this.selectedPayTypes = [THREE_PAY_TYPE.BALANCE, otherPayType];
					} else {
						this.selectedPayTypes = [THREE_PAY_TYPE.BALANCE];
					}

					// 如果只选择了储值账户支付，则设置为当前支付方式
					if (this.selectedPayTypes.length === 1) {
						this.threePayType = item.type;
					}

					return;
				}

				// 处理微信和支付宝支付
				if (item.type === THREE_PAY_TYPE.WX || item.type === THREE_PAY_TYPE.ALI) {
					// 如果已经选择了储值账户支付且余额不足，可以选择其他支付方式（除了对公付款）
					if (this.selectedPayTypes.includes(THREE_PAY_TYPE.BALANCE) && this.canUseOtherPayment) {
						// 移除之前选择的其他支付方式（微信或支付宝）
						this.selectedPayTypes = this.selectedPayTypes.filter(type =>
							type === THREE_PAY_TYPE.BALANCE || type === item.type
						);

						// 添加新选择的支付方式
						if (!this.selectedPayTypes.includes(item.type)) {
							this.selectedPayTypes.push(item.type);
						}

						// 设置当前支付方式为新选择的
						this.threePayType = item.type;
					} else {
						// 当没有选择余额支付时，微信、支付宝、对公付款都是单选的
						this.selectedPayTypes = [item.type];
						this.threePayType = item.type;
					}
				}


			},

			// 判断支付类型是否被选中
			isPayTypeSelected(item) {
				return this.selectedPayTypes.includes(item.type) || this.threePayType === item.type;
			},

			// 判断支付类型是否被禁用
			isPayTypeDisabled(item) {
				// 如果余额为0，则禁用余额支付
				if (item.type === THREE_PAY_TYPE.BALANCE && this.totalBalance <= 0) {
					return true;
				}

				// 如果payInfo中的recharge_balance有值，就禁用对公支付
				if (item.type === THREE_PAY_TYPE.ToPublic && this.payInfo.recharge_balance) {
					return true;
				}

				// 如果选择了对公付款，则禁用余额支付
				if (this.selectedPayTypes.includes(THREE_PAY_TYPE.ToPublic) && item.type === THREE_PAY_TYPE.BALANCE) {
					return true;
				}

				// 如果选择了储值账户支付且余额不足，则禁用对公付款
				if (this.selectedPayTypes.includes(THREE_PAY_TYPE.BALANCE) && this.canUseOtherPayment && item.type === THREE_PAY_TYPE.ToPublic) {
					return true;
				}

				// 如果选择了储值账户支付且余额不足，且已经选择了一种其他支付方式，则禁用其他支付方式
				// if (this.selectedPayTypes.includes(THREE_PAY_TYPE.BALANCE) && this.canUseOtherPayment) {
				// 	const otherPayType = this.selectedPayTypes.find(type => type !== THREE_PAY_TYPE.BALANCE);
				// 	if (otherPayType && item.type !== THREE_PAY_TYPE.BALANCE && item.type !== otherPayType) {
				// 		return true;
				// 	}
				// }

				// 不再禁用其他支付选项，改为单选逻辑
				return false;
			},


			async handlePay() {
				this.feedback.loading({ title: '支付中...' })
				try {
					// 如果没有选择任何支付方式，提示用户选择
					if (this.selectedPayTypes.length === 0) {
						this.feedback.toast({ title: '请选择支付方式' });
						return;
					}

					// 处理对公付款
					if (this.selectedPayTypes.includes(THREE_PAY_TYPE.ToPublic)) {
						const { data: { show_qrcode } } = await this.$u.api.getPayToPublicKefu()
						this.toPublicKefuQrCode = show_qrcode
						this.toPublicPopupVisible = true
						return
					}

					this.paying = true
					const { main_order_no, order_no, is_cross } = this.payInfo
					this.payParams = {
						main_order_no: main_order_no || order_no,
						is_cross
					}

					// 处理储值账户支付
					if (this.selectedPayTypes.includes(THREE_PAY_TYPE.BALANCE)) {
						// 先使用余额支付
						const balancePayResult = await this.processBalancePay();

						// 如果余额支付已全额支付，则直接返回
						if (balancePayResult.isPaid) {
							return;
						}

						// 如果没有选择其他支付方式，但余额不足，则提示用户选择其他支付方式
						if (this.selectedPayTypes.length === 1) {
							this.feedback.toast({ title: '余额不足，请选择其他支付方式' });
							return;
						}

						// 如果选择了其他支付方式，则继续使用其他支付方式支付剩余金额
						// 更新当前支付方式为选择的其他支付方式
						this.threePayType = this.selectedPayTypes.find(type => type !== THREE_PAY_TYPE.BALANCE);
					}

					// 处理其他支付方式
					switch (this.threePayType){
						case THREE_PAY_TYPE.WX:
							if (this.isAppPay) {
								this.wxPay()
							} else {
								if (this.isWxProcess) {
									await this.wxH5WxPay()
								} else {
									this.h5WxPay()
								}
							}
							break
						case THREE_PAY_TYPE.ALI:
							this.isAppPay ? this.aliPay() : this.h5AliPay()
							break
					}
				} finally {
					this.paying = false
					this.feedback.hideLoading()
				}
			},

			// 处理余额支付
			async processBalancePay() {
				try {
					// 调用余额支付接口
					const params = {
						main_order_no: this.payParams.main_order_no,
						balance_amount: this.useBalanceAmount
					};

					const res = await this.$u.api.balancePay(params);
					const { is_paid, pending_amount, recharge_balance, bonus_balance } = res.data;

					// 更新余额信息
					this.balanceInfo.recharge_balance = recharge_balance;
					this.balanceInfo.bonus_balance = bonus_balance;

					if (is_paid) {
						// 全额支付成功，直接跳转到支付成功页面
						this.feedback.toast({ title: '支付成功' });
						setTimeout(() => {
							const { payPlate } = this.payInfo;
							const { pDWinePartyOrderList, pBPaySuccess, pEMyOrder } = this.routeTable;
							let redirectToUrl = payPlate === 1 ? pDWinePartyOrderList : pBPaySuccess;
							if (payPlate === 7) {
								redirectToUrl = pEMyOrder;
							}
							this.jump.appAndMiniJump(0, redirectToUrl, this.$vhFrom, 1);
						}, 1500);

						return { isPaid: true };
					} else {
						// 部分支付，需要继续选择其他支付方式支付剩余金额
						this.feedback.toast({ title: `已使用余额支付${this.useBalanceAmount.toFixed(2)}元，还需支付${pending_amount}元` });

						// 更新原始支付金额为剩余待支付金额
						this.originalPaymentAmount = parseFloat(pending_amount);

						return { isPaid: false, pendingAmount: pending_amount };
					}
				} catch (e) {
					console.error('余额支付失败', e);
					this.feedback.toast({ title: '余额支付失败，请重试' });
					return { isPaid: false, error: e };
				}
			},
			async wxH5WxPay() { // 微信环境下拉起微信支付
				this.payment_method = 2
				const { main_order_no, is_cross } = this.payParams
				const { winePartyInfo, pageFullPath = '' } = this.payInfo
				const { party_id = '', package_id = '', gid = '' } = winePartyInfo || {}
				const queryParams = {
					vhType: this.payInfo.payPlate,
					vhPartyId: party_id,
					vhPackageId: package_id,
					vhGid: gid,
					pageFullPath
				}
				const queryStr = Object.keys(queryParams).map(key => `${key}=${queryParams[key]}`).join('&')
				const return_url = `${location.origin}${this.routeTable.pPaySuccessJump}?${queryStr}`

				var params={};
				if(this.payInfo.payPlate == 60){
					params= {
					main_order_no,
					payment_method: 2,
					order_type: 60,
					is_cross:false,
					return_url,
					payment_amount:this.payInfo.payment_amount
				}
				} else {
					params	= {
					main_order_no,
					payment_method: 2,
					order_type: 2,
					is_cross,
					return_url
				}
				}
				const res = await this.$u.api.payMethod(params)
				location.href = res.data.h5_pay_info
			},
			h5WxPay() { // 浏览器拉起微信小程序支付
				console.log('111990--h5WxPay');
				this.payment_method = 4
				const { token, uid } = uni.getStorageSync('loginInfo') || {}
				const { main_order_no, is_cross } = this.payParams
				var query={};
				if(this.payInfo.payPlate == 60){
					query= {
					main_order_no,
					payment_method: 4,
					order_type: 60,
					is_cross,
					token,
					uid,
					from: 5,
					versionCode: 100,
					payment_amount:this.payInfo.payment_amount
				}
				} else {
					query	= {
						main_order_no,
						payment_method: 4,
						order_type: 2,
						is_cross,
						token,
						uid,
						from: 5,
						versionCode: 100
				}
				}

				const queryStr = Object.keys(query).map(key => `${key}=${query[key]}`).join('&')
				console.log('queryStr', queryStr)
				// window.open(`${this.h5WxPayOrigin}?${queryStr}`)
				const href = `${this.h5WxPayOrigin}?${queryStr}`
				if (this.isIosSmzdmapp ) {
					location.href = href
					return
				}
				const newTab = window.open('/')
				if (newTab) {
					newTab.location = href
				} else {
					location.href = href
				}
			},
			async h5AliPay() {
				let newTab = ''
				if (!this.isIosSmzdmapp) {
					console.log('newTab-----------');
					newTab = window.open('/')
				}
				console.log('111990--h5AliPay');

				this.payment_method = 1
				const { main_order_no, is_cross } = this.payParams
				const { origin } = location
				const { pDWinePartyOrderList, pEMyOrder, pBOrderDeposit } = this.routeTable
				let return_url = this.payInfo.payPlate === 1 ? `${origin}${pDWinePartyOrderList}` : `${origin}${pEMyOrder}?myOrderStatus=1`
				if (this.payInfo.payPlate === 6) {
					return_url = `${origin}${pBOrderDeposit}`
				}
				var params={};
				if(this.payInfo.payPlate == 60){
					params= {
					main_order_no,
					payment_method: 1,
					order_type: 60,
					is_cross:false,
					return_url,
					payment_amount:this.payInfo.payment_amount
				}
				} else {
					params	= {
					main_order_no,
					payment_method: 1,
					order_type: 2,
					is_cross,
					return_url
				}
				}

				console.log('33333333333----', params);

				console.log('return_url------', return_url);
				const res = await this.$u.api.payMethod(params)
				const h5_pay_info = res.data.h5_pay_info
				console.log('res.data------', JSON.stringify(res.data));
				console.log('h5_pay_info------', h5_pay_info);
				if (newTab) {
					newTab.location = h5_pay_info
				} else {
					location.href = h5_pay_info
				}
			},
			async aliPay () {
				console.log('111990--aliPay');
				const { $paySuccessReturnUrl = '', pageFullPath } = this.payInfo
				const returnUrl = $paySuccessReturnUrl || pageFullPath
				let source = MAppPaymentSource.CrossBorder
				if (this.payInfo.payPlate === 4) source = MAppPaymentSource.AuctionOrder
				const params = {
					source,
					main_order_no: this.payParams.main_order_no,
					payment_method: MPaymentMethod.AliH5,
					return_url: `${location.origin}${returnUrl}`
				}
				const res = await this.$u.api.appPayment(params)
				this.feedback.loading({ title: '支付中...' })
				this.ailPayForm = res?.data?.pay_info || ''
				this.$nextTick(() => {
					document?.forms['alipay_submit']?.submit()
					this.feedback.hideLoading()
				})
			},
			async wxPay () {
				console.log('111990--wxPay');
				if (!this.wxPayOpenId) return
				let source = MAppPaymentSource.CrossBorder
				if (this.payInfo.payPlate === 4) source = MAppPaymentSource.AuctionOrder
				const params = {
					source,
					main_order_no: this.payParams.main_order_no,
					payment_method: MPaymentMethod.WxJSAPI,
					open_id: this.wxPayOpenId,
				}
				const res = await this.$u.api.appPayment(params)
				const data = res?.data?.pay_info || {}
				const { timeStamp, nonceStr, signType, paySign } = data
				wx.chooseWXPay({
					timestamp: timeStamp,
					nonceStr,
					package: data.package,
					signType,
					paySign,
				})
			},
			loadWxPayOpenId () {
				this.$u.api.getWxPayOpenIdByCode({ code: this.code, genre: 1 }).then(res => {
					this.wxPayOpenId = res?.data?.openid || ''
				}).catch(() => {
					const query = this.pages.getCurrenPage().$page.options
					const queryStr = Object.keys(query)
						.filter((key) => key !== 'code')
						.map((key) => `${key}=${query[key]}`)
						.join('&')
					const href = `${location.pathname}${queryStr ? `?${queryStr}` : ''}`
					location.href = href
				})
			},
			wxConfig () {
				let url = window.location.href.split("#")[0]
				if (this.$isDev) {
					url = 'https://activity.vinehoo.com/activities-v3/WechatCode'
				}
				this.$u.api.getJsapiSign({ url, appid: WX_APPID_PROD }).then(res => {
					const { appid, noncestr, sign, timestamp } = res || {}
					const configData = {
						debug: this.$isDev,
						appId: appid,
						nonceStr: noncestr,
						signature: sign,
						timestamp,
						jsApiList: ['chooseWXPay'],
					}
					console.log('configData', configData)
					wx.config(configData)
				})
			},

			// 查询订单状态
			async getQueryUmsPayOrderStatus() {
				try{
					// 若没有在当前页面点击过支付按钮，则不用查询订单状态
					if (!Object.keys(this.payParams).length) {
						return
					}
					this.feedback.loading({ title: '查询支付状态中...' })
					const { main_order_no } = this.payParams
					const params = { order_no: main_order_no }
					if(this.payInfo.payPlate == 60){
						const res = await this.$u.api.giftCardsOrderDetail(params)
						this.feedback.toast({ title: '查询成功~' })
						const { order_status = 0 } = res.data || {}
						if([1, 2, 3].includes(order_status)) {
							// 1 ? 酒会 : 商品
							const pageLength = getCurrentPages().length
							if (pageLength > 1) {
								const prevPage = getCurrentPages()[pageLength - 2]
								prevPage.$vm.coldChainUpgradeStatus = true
								this.jump.jumpPrePage(this.$vhFrom)
								return
							}
						}
						// this.jump.appAndMiniJump(0, this.routeTable.pJMyBalance, this.$vhFrom, 1)
					} else {
						const res = await this.$u.api.queryCommonOrderDetail(params)
					this.feedback.toast({ title: '查询成功~' })
					const { sub_order_status = 0 } = res.data || {}
					const { payPlate } = this.payInfo
					const { pDWinePartyOrderList, pBPaySuccess, pEMyOrder } = this.routeTable
					let redirectToUrl = ''
					if([1, 2, 3].includes(sub_order_status)) {
						// 1 ? 酒会 : 商品
						redirectToUrl = payPlate === 1 ? pDWinePartyOrderList : pBPaySuccess
						if (payPlate === 7) {
							const pageLength = getCurrentPages().length
							if (pageLength > 1) {
								const prevPage = getCurrentPages()[pageLength - 2]
								prevPage.$vm.coldChainUpgradeStatus = true
								this.jump.jumpPrePage(this.$vhFrom)
								return
							} else {
								redirectToUrl = pEMyOrder
							}
						}
					}else{
						redirectToUrl = payPlate === 1 ? pDWinePartyOrderList : pEMyOrder
					}
					// this.jump.redirectTo(redirectToUrl)
					this.jump.appAndMiniJump(0,redirectToUrl, this.$vhFrom, 1)
					}

				}catch(e){
					//TODO handle the exception
				}
			},

			// 倒计时结束
			countDownEnd() {
				const { payPlate } = this.payInfo
				switch (payPlate) {
					case 3:
						// this.jump.navigateBack()
						this.jump.jumpPrePage(this.$vhFrom)
						break
					case 4:
						// this.jump.navigateBack()
						this.jump.jumpPrePage(this.$vhFrom)
						break
					case 6: //订金
					this.jump.appAndMiniJump(0, this.$routeTable.pBOrderDeposit, this.$vhFrom, 1)
						// this.jump.redirectTo(this.$routeTable.pBOrderDeposit)
						break
					default:
						// this.jump.redirectTo(this.routeTable.pEMyOrder)
						this.jump.appAndMiniJump(0,this.routeTable.pEMyOrder, this.$vhFrom, 1)
				}
			},

			// 离开支付
			leave() {
				switch( this.payInfo.payPlate ) {
					case 1: //酒会
					this.jump.jumpPrePage(this.$vhFrom)
					break
					case 3:
					this.jump.jumpPrePage(this.$vhFrom)
						break
					case 4:
						this.jump.jumpPrePage(this.$vhFrom)
						break
					case 6: //订金
					this.jump.appAndMiniJump(0, this.$routeTable.pBOrderDeposit, this.$vhFrom, 1)
						// this.jump.redirectTo(this.$routeTable.pBOrderDeposit)
						break
						case 60:
						this.jump.jumpPrePage(this.$vhFrom)
						break
					default: //普通商品订单
					// this.jump.redirectTo(this.routeTable.pEMyOrder)
					console.log('跳转到我的订单----');

					this.jump.appAndMiniJump(0, this.routeTable.pEMyOrder, this.$vhFrom, 1)
				}
			},
			queryOrderStatus () {
				const { payPlate } = this.payInfo
				if (this.payInfo.payPlate === 3) {
					this.queryAuctionEarnestOrderStatus()
				} else if (this.payInfo.payPlate === 4) {
					this.queryAuctionGoodsOrderStatus()
				} else if( payPlate === 6 ) {
					this.queryDepositOrderStatus()
				} else {
					this.getQueryUmsPayOrderStatus()
				}
			},
			async queryAuctionEarnestOrderStatus () {
				try{
					if (!Object.keys(this.payParams).length) {
						return
					}
					this.feedback.loading({ title: '查询支付状态中...' })
					const { main_order_no } = this.payParams
					const params = { main_order_no }
					await this.$u.api.getAuctionEarnestOrderDetail(params)
					this.feedback.toast({ title: '查询成功~' })
					// if (res?.data?.status === 1) this.SET_AUCTION_EARNEST_PAY_STATUS(true)
					this.jump.jumpPrePage(this.$vhFrom)
				} catch (e) {
				}
			},
			async queryAuctionGoodsOrderStatus () {
				try{
					if (!Object.keys(this.payParams).length) {
						return
					}
					const res = await this.$u.api.auctionOrderDetail({ order_no: this.payParams.main_order_no })
					const { order_status = 0 } = res?.data || {}
					if ([1, 2, 3].includes(order_status)) {
						this.jump.jumpPrePage(this.$vhFrom)
					}
				} catch (e) {
				}
			},
			// 查询订金订单状态
			async queryDepositOrderStatus () {
				try{
					if (!Object.keys(this.payParams).length) return
					const res = await this.$u.api.queryCommonOrderDetail({ order_no: this.payParams.main_order_no })
					const { sub_order_status = 0 } = res?.data || {}
					if ([1, 3, 4].includes(sub_order_status)) {
						// this.jump.redirectTo(this.$routeTable.pBOrderDeposit)
						this.jump.appAndMiniJump(0,this.$routeTable.pBOrderDeposit, this.$vhFrom, 1)

					}else if([0].includes(sub_order_status)) {
						this.feedback.toast({ title: '您取消了支付~' })
						setTimeout(() => {
							this.jump.jumpPrePage(this.$vhFrom)
						}, 1500)
					}
				} catch (e) {
				}
			},
			async loadOrderInfo () {
				if (this.payPlate === 4) {
					const res = await this.$u.api.auctionOrderDetail({ order_no: this.orderNo })
					const { order_no, countdown, cash_amount} = res?.data || {}
					this.muPayInfo({
						payPlate: this.payPlate,
						main_order_no: order_no,
						payment_amount:cash_amount,
						countdown,
						is_cross: 0,
						$isAppPay: true,
					})

					// 保存原始支付金额
					this.originalPaymentAmount = parseFloat(cash_amount || 0);
				} else if (this.payPlate === 5) {
					const res = await this.$u.api.orderDetail({ order_no: this.orderNo })
					const { order_no, countdown, cash_amount} = res?.data || {}
					this.muPayInfo({
						payPlate: this.payPlate,
						main_order_no: order_no,
						payment_amount:cash_amount,
						countdown,
						is_cross: 1,
						$isAppPay: true,
						$paySuccessReturnUrl: this.routeTable.pBPaySuccess
					})

					// 保存原始支付金额
					this.originalPaymentAmount = parseFloat(cash_amount || 0);
				}
			},
		}

	}
</script>

<style scoped>
.disabled {
  opacity: 0.5;
  pointer-events: none;
}
</style>
